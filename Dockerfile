# Build stage
#FROM node:18-alpine as build
ARG ACCOUNT_ID
FROM ${ACCOUNT_ID}.dkr.ecr.ap-south-1.amazonaws.com/node-current-alpine3.21-secured:v1 as builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
#FROM node:18-alpine
ARG ACCOUNT_ID
FROM ${ACCOUNT_ID}.dkr.ecr.ap-south-1.amazonaws.com/node-current-alpine3.21-secured:v1

WORKDIR /app

# Install serve to serve the built application
RUN npm install -g serve

# Copy built application from build stage
COPY --from=builder /app/dist ./dist

# Expose port 3000
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/ || exit 1

# Serve the application on port 3000
CMD ["serve", "-s", "dist", "-l", "tcp://0.0.0.0:3000"]
