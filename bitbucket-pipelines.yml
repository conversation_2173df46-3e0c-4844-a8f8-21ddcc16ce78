image: node:18
definitions:
  steps:
    - step: &build
        name: Build, test and Deploy image to bitbucket
        caches:
          - docker
        script:
          - echo "Installing AWS CLI..."
          - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          - unzip awscliv2.zip
          - ./aws/install
          - aws --version 
          - export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token
          - export AWS_ROLE_ARN=$OIDC_ROLE_ARN
          - export AWS_DEFAULT_REGION=$AWS_REGION
          - echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
          - echo ${NPMRC_TOKEN} >> .npmrc
          #- aws secretsmanager get-secret-value --secret-id ${AWS_MAIN_SM_NAME}  --region ${AWS_REGION} |   jq -r '.SecretString' |   jq -r "to_entries|map(\"\(.key)=\\\"\(.value|tostring)\\\"\")|.[]" > .env
          - ls -la
          - echo ${AWS_MAIN_SM_NAME}
          - npm i
          - npm run build
          - aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin $ACCOUNT_ID.dkr.ecr.ap-south-1.amazonaws.com
          - docker build --platform=linux/amd64 --build-arg ACCOUNT_ID=$ACCOUNT_ID -t $IMAGE_NAME -f ${DOCKER_FILE_FULL_PATH} --no-cache .
          - pipe: atlassian/aws-ecr-push-image:1.6.2
            variables:
              AWS_OIDC_ROLE_ARN: $OIDC_ROLE_ARN
              AWS_DEFAULT_REGION: '${AWS_REGION}'
              IMAGE_NAME: "${IMAGE_NAME}"
              TAGS: "${version} latest "
          - aws ecs update-service --cluster $CLUSTER_NAME  --service $SERVICE_NAME --force-new-deployment --region ap-south-1
          - echo "Will update the Task Definition here if any , todo provide a CICD implementation "


        services:
          - docker


pipelines:
  branches:
    # runs test on master commits
    master:
      - step:
          oidc: true
          deployment: production
          <<: *build


  tags:
    # Tags with QA will be  pushed to staging
    QA:
      - step:
          oidc: true
          deployment: staging
          <<: *build