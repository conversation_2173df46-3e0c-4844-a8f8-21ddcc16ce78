
# Project Architecture

## Technologies Used

- **Frontend**: React 18, TypeScript, Vite
- **UI Framework**: Tailwind CSS, shadcn/ui components
- **State Management**: React Context, TanStack Query
- **Authentication**: Azure MSAL (Microsoft SSO)
- **Cloud Storage**: AWS S3 SDK
- **Routing**: React Router DOM

## Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── Auth/            # Authentication components
│   ├── ConfigEditor/    # Configuration editing components
│   ├── Dashboard/       # Main dashboard components
│   │   ├── ConfigCard.tsx
│   │   ├── ConfigUrlSection.tsx    # URL copying functionality
│   │   ├── CreateConfigDialog.tsx
│   │   ├── Dashboard.tsx
│   │   ├── PromotionDialog.tsx
│   │   └── S3ConfigDialog.tsx
│   ├── Layout/          # Layout components (Header, etc.)
│   ├── Preview/         # Configuration preview components
│   └── ui/              # shadcn/ui base components
├── contexts/            # React contexts
│   ├── AuthContext.tsx  # Authentication state management
│   └── ConfigContext.tsx # Configuration state management
├── pages/               # Route components
│   ├── Index.tsx        # Main dashboard page
│   ├── Login.tsx        # Authentication page
│   └── NotFound.tsx     # 404 page
├── services/            # Business logic and API services
│   ├── authService.ts   # Authentication service
│   ├── s3Service.ts     # AWS S3 integration
│   ├── fileStorageService.ts # Storage abstraction layer
│   └── promotionService.ts   # Configuration promotion logic
└── types/               # TypeScript type definitions
```

## Features

### Environment Management

The application supports three environments:
- **Development**: For active development and testing
- **Staging**: For pre-production validation
- **Production**: For live configurations

Switch between environments using the dropdown in the header.

### Configuration Management

- **Create**: Add new configurations with JSON-based editing
- **Edit**: Modify existing configurations with visual editor
- **Delete**: Remove configurations (with confirmation)
- **Version Control**: Track configuration versions and changes
- **URL Access**: Copy direct URLs for configurations in any environment

### Configuration URLs

The ConfigUrlSection component provides:
- **Environment-specific URLs**: Direct links to configurations for each environment
- **One-click copying**: Copy URLs to clipboard with toast feedback
- **S3 integration**: URLs point to S3 storage when configured, localStorage fallback otherwise

### Configuration Promotion

Promote configurations between environments:
1. **Dynamic promotion indicators**: Shows actual count of configurations with changes
2. **Review differences**: Compare configurations between source and target environments
3. **One-click promotion**: Deploy configurations with automatic URL generation
4. **Consistent URLs**: Generated URLs remain stable across promotions

### Dashboard Features

- **Real-time promotion counts**: Dynamically updates promotion candidate indicators
- **Environment filtering**: View configurations specific to selected environment
- **Bulk operations**: Duplicate, delete, and manage multiple configurations
- **S3 connectivity status**: Visual indicators for cloud storage connection

## Key Components

### Dashboard Component
- Central hub for configuration management
- Dynamic promotion candidate counting
- Environment-specific configuration filtering
- Integration with all major features

### ConfigUrlSection Component
- Displays copyable URLs for current environment
- Integrates with S3 service for URL generation
- Provides user feedback through toast notifications

### Promotion System
- Automatic detection of configuration changes
- Environment-aware promotion workflows
- Consistent URL generation for promoted configs
