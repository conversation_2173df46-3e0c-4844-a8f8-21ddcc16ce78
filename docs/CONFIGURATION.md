
# Configuration Guide

## Authentication Configuration

### Microsoft SSO Setup

To enable Microsoft SSO authentication:

1. Register your application in Azure Active Directory
2. Update the client ID in `src/pages/Login.tsx`:
```typescript
const msalConfig = {
  auth: {
    clientId: 'YOUR_MICROSOFT_CLIENT_ID', // Replace with your Azure AD Client ID
    // ... rest of config
  }
};
```
3. Configure redirect URIs in your Azure AD app registration

## AWS S3 Configuration (Optional)

The application supports AWS S3 for configuration storage. If not configured, it automatically falls back to localStorage.

### Configuration Through UI (Recommended)

S3 configuration is managed through the application interface, not environment files:

1. Click the "S3 Config" button in the dashboard header
2. Enter your AWS credentials:
   - AWS Access Key ID
   - AWS Secret Access Key  
   - S3 Bucket Name
   - AWS Region
3. Configuration is stored in browser localStorage and used for the current session

### S3 Setup Requirements

1. **Create S3 bucket** with appropriate permissions
2. **Configure CORS policy** to allow browser access:
```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
    "AllowedOrigins": ["*"],
    "ExposeHeaders": []
  }
]
```
3. **Set bucket policy** for configuration access
4. **Create IAM user** with S3 permissions for the specific bucket

### Why No .env Files?

This Lovable-based application doesn't use traditional `.env` files because:
- Configuration is managed through the UI for better user experience
- S3 credentials are stored securely in browser localStorage
- Environment-specific settings are handled through the application's environment selector
- Sensitive data should use Supabase integration or similar secure services

## Storage Options

### AWS S3 Backend (Recommended for production)
- Persistent storage across sessions and devices
- Multi-user support and collaboration
- Backup and versioning capabilities
- Consistent URLs for configuration access
- Real-time promotion candidate detection

### localStorage Fallback (Development)
- Local browser storage only
- No external dependencies
- Suitable for development and testing
- Limited to single browser/device

## URL Generation

### S3-based URLs
When S3 is configured, configurations get consistent URLs like:
```
https://your-bucket.s3.amazonaws.com/promoted-configs/staging/config-name.json
```

### localStorage URLs
When using localStorage, configurations are accessed through the application interface only.

## Environment Management

The application manages three environments internally:
- **Development** (`dev`): For active development work
- **Staging** (`staging`): For pre-production testing  
- **Production** (`prod`): For live configurations

Configuration promotion flows:
- `dev` → `staging` → `prod`
- Real-time detection of promotable configurations
- Dynamic counting of promotion candidates

## Security Considerations

- Store Azure AD Client ID securely in code
- Use IAM roles for AWS S3 access in production environments
- Implement proper CORS policies for S3 buckets
- Validate all configuration inputs on both client and server side
- Use HTTPS in all production environments
- Consider AWS credentials rotation policies
- Monitor S3 access logs for security audit trails
