
# ECS Deployment Guide

This guide explains how to deploy the Configuration Management Application to AWS ECS using Bitbucket Pipelines.

## Prerequisites

### AWS Resources
1. **ECS Cluster**: Create an ECS cluster (Fargate recommended)
2. **ECR Repository**: Create an ECR repository to store Docker images
3. **VPC and Subnets**: Ensure you have a VPC with public/private subnets
4. **Security Groups**: Configure security groups to allow HTTP traffic
5. **Application Load Balancer** (optional): For production deployments
6. **IAM Roles**: 
   - ECS Task Execution Role
   - ECS Task Role
   - Bitbucket deployment role

### Bitbucket Configuration
Set up the following repository variables in Bitbucket:
- `AWS_ACCESS_KEY_ID`: AWS access key for deployment
- `AWS_SECRET_ACCESS_KEY`: AWS secret key for deployment
- `AWS_DEFAULT_REGION`: AWS region (e.g., us-east-1)
- `AWS_ACCOUNT_ID`: Your AWS account ID
- `ECR_REPOSITORY_NAME`: Name of your ECR repository
- `ECS_CLUSTER_NAME`: Name of your ECS cluster
- `ECS_SERVICE_NAME`: Name of your ECS service

## Setup Steps

### 1. Create ECR Repository
```bash
aws ecr create-repository --repository-name config-management --region us-east-1
```

### 2. Create ECS Cluster
```bash
aws ecs create-cluster --cluster-name config-management-cluster --capacity-providers FARGATE
```

### 3. Register Task Definition
Update `ecs-task-definition.json` with your specific values and register:
```bash
aws ecs register-task-definition --cli-input-json file://ecs-task-definition.json
```

### 4. Create ECS Service
```bash
aws ecs create-service \
  --cluster config-management-cluster \
  --service-name config-management-service \
  --task-definition config-management-app:1 \
  --desired-count 1 \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[subnet-12345],securityGroups=[sg-12345],assignPublicIp=ENABLED}"
```

### 5. Configure Bitbucket Variables
Go to your Bitbucket repository → Repository settings → Pipelines → Repository variables and add:

| Variable | Value | Secured |
|----------|-------|---------|
| AWS_ACCESS_KEY_ID | Your AWS access key | Yes |
| AWS_SECRET_ACCESS_KEY | Your AWS secret key | Yes |
| AWS_DEFAULT_REGION | us-east-1 | No |
| AWS_ACCOUNT_ID | ************ | No |
| ECR_REPOSITORY_NAME | config-management | No |
| ECS_CLUSTER_NAME | config-management-cluster | No |
| ECS_SERVICE_NAME | config-management-service | No |

## Pipeline Workflow

### Main Branch Deployment
1. Code is pushed to `main` branch
2. Dependencies are installed and application is built
3. Docker image is built and tagged
4. Image is pushed to ECR
5. ECS service is updated with new image
6. Pipeline waits for deployment to stabilize

### Pull Request Validation
1. Build and test the application
2. Ensure no breaking changes
3. No deployment occurs for PRs

## Monitoring and Troubleshooting

### CloudWatch Logs
Monitor application logs in CloudWatch:
```bash
aws logs describe-log-groups --log-group-name-prefix "/ecs/config-management"
```

### ECS Service Status
Check service status:
```bash
aws ecs describe-services --cluster config-management-cluster --services config-management-service
```

### Rolling Back
To rollback to a previous version:
1. Find the previous task definition revision
2. Update the service to use the previous revision:
```bash
aws ecs update-service --cluster config-management-cluster --service config-management-service --task-definition config-management-app:PREVIOUS_REVISION
```

## Security Considerations

1. **IAM Permissions**: Use least-privilege access for all IAM roles
2. **Network Security**: Deploy in private subnets with NAT Gateway
3. **Environment Variables**: Use AWS Systems Manager Parameter Store for secrets
4. **Container Security**: Regularly update base images and scan for vulnerabilities
5. **Access Control**: Implement proper authentication and authorization

## Cost Optimization

1. **Right-sizing**: Monitor CPU and memory usage to optimize task definition
2. **Auto Scaling**: Configure ECS service auto scaling based on demand
3. **Spot Instances**: Consider Fargate Spot for cost savings
4. **Resource Cleanup**: Implement lifecycle policies for ECR images

## Production Checklist

- [ ] Configure Application Load Balancer
- [ ] Set up auto scaling policies
- [ ] Configure health checks
- [ ] Set up monitoring and alerting
- [ ] Implement blue-green deployments
- [ ] Configure backup strategies
- [ ] Set up log aggregation
- [ ] Implement security scanning
- [ ] Configure DNS and SSL certificates
- [ ] Set up disaster recovery procedures
