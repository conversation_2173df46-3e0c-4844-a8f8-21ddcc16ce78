
# Setup Guide

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn package manager

## Installation

1. Clone the repository:
```bash
git clone <YOUR_GIT_URL>
cd <YOUR_PROJECT_NAME>
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

The application will be available at `http://localhost:5173`

## Development Scripts

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run type checking
npm run type-check
```

## Environment Variables

The application doesn't use traditional .env files. Configuration is managed through:
- Azure AD settings (hardcoded in Login component)
- S3 configuration (stored in application state/localStorage)
- Runtime environment selection (dev/staging/prod)
