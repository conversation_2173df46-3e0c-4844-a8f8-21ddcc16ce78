
# Document Versioning System Setup Guide

## Overview

This document versioning system provides comprehensive version control for JSON configuration documents using AWS S3 as the storage backend. It maintains complete document history, tracks changes, and provides metadata for each version.

## Architecture

The system consists of several key components:

- **S3Service**: Handles AWS S3 operations (upload, download, list, etc.)
- **DocumentVersioningService**: Core versioning logic and document management
- **VersioningPanel**: React component for viewing version history
- **VersioningDemo**: Demo component to test the system

## Installation

### Prerequisites

- Node.js and npm installed
- AWS S3 bucket (or S3-compatible storage)
- AWS credentials (for production use)

### Dependencies

The system uses the following existing dependencies:
- React 18+
- TypeScript
- Tailwind CSS
- shadcn/ui components
- Lucide React (for icons)

No additional dependencies need to be installed.

## Configuration

### 1. AWS S3 Setup

#### For Production:
1. Create an S3 bucket in your AWS account
2. Configure bucket permissions for read/write access
3. Create IAM user with S3 access permissions
4. Generate access key and secret key

#### For Development/Demo:
The system includes mock S3 operations for testing without actual AWS credentials.

### 2. Service Configuration

Update the S3 configuration in your services:

```typescript
const s3Config = {
  bucketName: 'your-bucket-name',
  region: 'us-east-1', // or your preferred region
  accessKeyId: 'your-access-key',
  secretAccessKey: 'your-secret-key'
};
```

### 3. Environment Setup

The system supports three environments:
- `dev` - Development environment
- `staging` - Staging environment  
- `prod` - Production environment

## File Structure

The system organizes files in S3 using this structure:

```
/{environment}/{document-type}/{document-id}/{version}/
├── {document-id}_v{version}.json          # Document data
└── {document-id}_v{version}_metadata.json # Version metadata
```

### Example:
```
/dev/modal-config/winner-popup-config/v1/
├── winner-popup-config_v1.json
└── winner-popup-config_v1_metadata.json

/dev/modal-config/winner-popup-config/v2/
├── winner-popup-config_v2.json
└── winner-popup-config_v2_metadata.json
```

## Usage

### 1. Initialize Services

```typescript
import { S3Service } from '@/services/s3Service';
import { DocumentVersioningService } from '@/services/documentVersioningService';

// Configure S3
const s3Config = {
  bucketName: 'my-config-documents',
  region: 'us-east-1',
  accessKeyId: 'your-access-key',
  secretAccessKey: 'your-secret-key'
};

// Initialize services
const s3Service = new S3Service(s3Config);
const versioningService = new DocumentVersioningService(s3Service);
```

### 2. Create New Document

```typescript
const createRequest = {
  documentId: 'winner-popup-config',
  documentType: 'modal-config',
  environment: 'dev' as const,
  author: '<EMAIL>',
  changeDescription: 'Initial configuration',
  data: {
    // Your document data here
    title: 'Congratulations!',
    backgroundColor: '#4F46E5'
  }
};

const result = await versioningService.createDocument(createRequest);
```

### 3. Update Document

```typescript
const updateRequest = {
  documentId: 'winner-popup-config',
  documentType: 'modal-config',
  environment: 'dev' as const,
  author: '<EMAIL>',
  changeDescription: 'Updated colors and added new button',
  data: {
    // Updated document data
    title: 'Amazing Victory!',
    backgroundColor: '#7C3AED'
  }
};

const result = await versioningService.updateDocument(updateRequest);
```

### 4. Get Version History

```typescript
const history = await versioningService.getVersionHistory(
  'winner-popup-config',
  'modal-config',
  'dev'
);

console.log('Latest version:', history.latestVersion);
console.log('All versions:', history.versions);
```

### 5. Get Specific Version

```typescript
const version1 = await versioningService.getDocumentByVersion(
  'winner-popup-config',
  'modal-config',
  'dev',
  1
);

console.log('Version 1 data:', version1.data);
```

## React Components

### VersioningPanel Component

Add version history to your application:

```typescript
import VersioningPanel from '@/components/ConfigEditor/VersioningPanel';

<VersioningPanel
  documentId="winner-popup-config"
  documentType="modal-config"
  environment="dev"
  onVersionSelect={(version) => console.log('Selected version:', version)}
/>
```

### VersioningDemo Component

Test the system with the demo component:

```typescript
import VersioningDemo from '@/components/ConfigEditor/VersioningDemo';

<VersioningDemo />
```

## API Reference

### DocumentVersioningService Methods

#### `createDocument(request: CreateDocumentRequest): Promise<DocumentResponse>`
Creates a new document with version 1.

#### `updateDocument(request: UpdateDocumentRequest): Promise<DocumentResponse>`
Updates an existing document, creating a new version.

#### `getVersionHistory(documentId, documentType, environment): Promise<VersionHistory>`
Retrieves complete version history for a document.

#### `getDocumentByVersion(documentId, documentType, environment, version): Promise<DocumentVersion>`
Retrieves a specific version of a document.

### S3Service Methods

#### `uploadFile(key: string, content: string): Promise<boolean>`
Uploads a file to S3.

#### `downloadFile(key: string): Promise<string | null>`
Downloads a file from S3.

#### `listFiles(prefix: string): Promise<string[]>`
Lists all files with a given prefix.

#### `fileExists(key: string): Promise<boolean>`
Checks if a file exists in S3.

## Data Types

### DocumentVersion
```typescript
interface DocumentVersion {
  id: string;
  documentId: string;
  version: number;
  environment: 'dev' | 'staging' | 'prod';
  timestamp: string;
  author: string;
  changeDescription?: string;
  data: Record<string, any>;
}
```

### DocumentMetadata
```typescript
interface DocumentMetadata {
  documentId: string;
  version: number;
  environment: 'dev' | 'staging' | 'prod';
  timestamp: string;
  author: string;
  changeDescription?: string;
  fileSize: number;
  checksum: string;
}
```

## Testing

### Run Demo
Use the VersioningDemo component to test the system:

1. Navigate to your application
2. Add the VersioningDemo component to a page
3. Click "Run Demo" to see the system in action
4. Check browser console for detailed output

### Manual Testing

```typescript
import { runDocumentVersioningDemo } from '@/services/documentVersioningDemo';

// Run the demo programmatically
await runDocumentVersioningDemo();
```

## Error Handling

The system includes comprehensive error handling:

- **Document already exists**: Returns error when trying to create existing document
- **Document not found**: Returns error when trying to update non-existent document
- **S3 upload failures**: Handles network and permission errors
- **Invalid data**: Validates input parameters

## Security Considerations

### Production Deployment

1. **Never commit AWS credentials** to your repository
2. **Use environment variables** for sensitive configuration
3. **Configure S3 bucket policies** to restrict access
4. **Use IAM roles** instead of access keys when possible
5. **Enable S3 versioning** for additional backup protection

### Recommended S3 Bucket Policy

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::YOUR-ACCOUNT:user/YOUR-USER"
      },
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::your-bucket-name",
        "arn:aws:s3:::your-bucket-name/*"
      ]
    }
  ]
}
```

## Troubleshooting

### Common Issues

1. **S3 Access Denied**
   - Check AWS credentials
   - Verify bucket permissions
   - Ensure IAM user has required permissions

2. **File Not Found**
   - Verify document exists
   - Check file path structure
   - Ensure correct environment/document type

3. **Version Conflicts**
   - System automatically increments versions
   - Use getVersionHistory to check existing versions

### Debug Mode

Enable detailed logging by checking browser console when running operations.

## Migration

### From Other Systems

To migrate existing documents:

1. Create new documents using `createDocument`
2. Import historical versions using `updateDocument`
3. Preserve original timestamps in metadata

### Backup Strategy

- S3 provides built-in durability
- Enable S3 versioning for additional protection
- Consider cross-region replication for critical data

## Performance Considerations

- **Batch operations**: Group multiple operations when possible
- **Caching**: Implement caching for frequently accessed versions
- **Cleanup**: Implement retention policies for old versions
- **Indexing**: Consider external indexing for large document sets

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review error messages in browser console
3. Verify AWS configuration and permissions
4. Test with the demo component first
