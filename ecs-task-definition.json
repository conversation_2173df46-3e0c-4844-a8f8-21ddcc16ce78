{"family": "config-management-app", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "executionRoleArn": "arn:aws:iam::YOUR_ACCOUNT_ID:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::YOUR_ACCOUNT_ID:role/ConfigManagementECSTaskRole", "containerDefinitions": [{"name": "config-management-container", "image": "YOUR_ACCOUNT_ID.dkr.ecr.YOUR_REGION.amazonaws.com/config-management:latest", "portMappings": [{"containerPort": 80, "protocol": "tcp"}], "essential": true, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/config-management-app", "awslogs-region": "YOUR_REGION", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}, "environment": [{"name": "NODE_ENV", "value": "production"}, {"name": "AWS_DEFAULT_REGION", "value": "YOUR_REGION"}, {"name": "S3_BUCKET_NAME", "value": "YOUR_S3_BUCKET_NAME"}]}]}