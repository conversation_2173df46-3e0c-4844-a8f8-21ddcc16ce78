
#!/bin/bash

# Health check script for the configuration management application
# This script can be used by monitoring systems to check application health

set -e

# Configuration
HOST=${HOST:-localhost}
PORT=${PORT:-80}
TIMEOUT=${TIMEOUT:-10}

# Function to check HTTP endpoint
check_http() {
    local url="http://${HOST}:${PORT}/health"
    echo "Checking HTTP endpoint: $url"
    
    if curl -f -s --max-time $TIMEOUT "$url" > /dev/null; then
        echo "✅ HTTP health check passed"
        return 0
    else
        echo "❌ HTTP health check failed"
        return 1
    fi
}

# Function to check if the main application is accessible
check_app() {
    local url="http://${HOST}:${PORT}/"
    echo "Checking application endpoint: $url"
    
    if curl -f -s --max-time $TIMEOUT "$url" > /dev/null; then
        echo "✅ Application health check passed"
        return 0
    else
        echo "❌ Application health check failed"
        return 1
    fi
}

# Main health check
main() {
    echo "Starting health check for Configuration Management Application"
    echo "Target: ${HOST}:${PORT}"
    echo "Timeout: ${TIMEOUT}s"
    echo "----------------------------------------"
    
    local exit_code=0
    
    # Check HTTP health endpoint
    if ! check_http; then
        exit_code=1
    fi
    
    # Check main application
    if ! check_app; then
        exit_code=1
    fi
    
    echo "----------------------------------------"
    if [ $exit_code -eq 0 ]; then
        echo "✅ All health checks passed"
    else
        echo "❌ Some health checks failed"
    fi
    
    exit $exit_code
}

# Run health check
main "$@"
