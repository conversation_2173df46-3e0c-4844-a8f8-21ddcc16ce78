
import React, { useState } from 'react';
import { ArrowLeft, Save, Eye, Code, RotateCcw } from 'lucide-react';
import { useConfig } from '@/contexts/ConfigContext';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import FormBuilder from './FormBuilder';
import JsonEditor from './JsonEditor';
import { toast } from '@/hooks/use-toast';

const ConfigEditor = () => {
  const { selectedConfig, setSelectedConfig, updateConfiguration, isLoading } = useConfig();
  const [activeTab, setActiveTab] = useState('visual');
  const [jsonData, setJsonData] = useState(
    selectedConfig ? JSON.stringify(selectedConfig.jsonData, null, 2) : ''
  );
  const [isSaving, setIsSaving] = useState(false);

  if (!selectedConfig) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">No configuration selected</p>
      </div>
    );
  }

  const handleSave = async () => {
    try {
      setIsSaving(true);
      const parsedJson = JSON.parse(jsonData);
      
      const success = await updateConfiguration(selectedConfig.id, {
        jsonData: parsedJson
      });
      
      if (!success) {
        // Error toast is already shown in updateConfiguration
        return;
      }
    } catch (error) {
      toast({
        title: "Invalid JSON",
        description: "Please fix the JSON syntax errors before saving.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleJsonChange = (newJson: string) => {
    setJsonData(newJson);
  };

  const handleFormChange = (newJsonData: Record<string, any>) => {
    setJsonData(JSON.stringify(newJsonData, null, 2));
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSelectedConfig(null)}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                {selectedConfig.name}
              </h1>
              <p className="text-sm text-gray-500">
                {selectedConfig.environment.toUpperCase()} • Version {selectedConfig.version}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <RotateCcw className="w-4 h-4 mr-2" />
              Revert
            </Button>
            <Button 
              onClick={handleSave} 
              disabled={isSaving || isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="w-4 h-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>
      </div>

      {/* Editor Content */}
      <div className="flex-1 p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <TabsList className="grid w-full grid-cols-2 max-w-md">
            <TabsTrigger value="visual" className="flex items-center space-x-2">
              <Eye className="w-4 h-4" />
              <span>Visual Editor</span>
            </TabsTrigger>
            <TabsTrigger value="json" className="flex items-center space-x-2">
              <Code className="w-4 h-4" />
              <span>JSON Editor</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="visual" className="h-full mt-4">
            <FormBuilder 
              config={selectedConfig}
              onConfigChange={handleFormChange}
            />
          </TabsContent>
          
          <TabsContent value="json" className="h-full mt-4">
            <JsonEditor 
              value={jsonData}
              onChange={handleJsonChange}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default ConfigEditor;
