
import React, { useState } from 'react';
import { Configuration } from '@/contexts/ConfigContext';
import ConfigBlock from './components/ConfigBlock';
import EmptyState from './components/EmptyState';

interface FormBuilderProps {
  config: Configuration;
  onConfigChange: (jsonData: Record<string, any>) => void;
}

const FormBuilder = ({ config, onConfigChange }: FormBuilderProps) => {
  const [jsonData, setJsonData] = useState(config.jsonData);
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({});

  const updateField = (blockKey: string, fieldKey: string, value: any) => {
    const updatedData = {
      ...jsonData,
      [blockKey]: {
        ...jsonData[blockKey],
        [fieldKey]: value
      }
    };
    setJsonData(updatedData);
    onConfigChange(updatedData);
  };

  const updateNestedField = (blockKey: string, value: any) => {
    const updatedData = {
      ...jsonData,
      [blockKey]: value
    };
    setJsonData(updatedData);
    onConfigChange(updatedData);
  };

  const toggleSection = (blockKey: string) => {
    setOpenSections(prev => ({
      ...prev,
      [blockKey]: !prev[blockKey]
    }));
  };

  return (
    <div className="space-y-6">
      {Object.entries(jsonData).map(([blockKey, blockData]) => (
        <div key={blockKey} id={blockKey}>
          <ConfigBlock
            blockKey={blockKey}
            blockData={blockData}
            onUpdate={updateField}
            onNestedUpdate={updateNestedField}
            isOpen={openSections[blockKey] || false}
            onToggle={() => toggleSection(blockKey)}
          />
        </div>
      ))}
      
      {Object.keys(jsonData).length === 0 && <EmptyState />}
    </div>
  );
};

export default FormBuilder;
