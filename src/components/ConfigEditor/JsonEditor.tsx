
import React from 'react';
import { Textarea } from '@/components/ui/textarea';

interface JsonEditorProps {
  value: string;
  onChange: (value: string) => void;
}

const JsonEditor = ({ value, onChange }: JsonEditorProps) => {
  return (
    <div className="h-full space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Raw JSON Configuration</h3>
        <div className="text-sm text-gray-500">
          Make sure your JSON is valid before saving
        </div>
      </div>
      
      <Textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="h-96 font-mono text-sm resize-none"
        placeholder="Enter your JSON configuration here..."
      />
      
      <div className="text-xs text-gray-500 space-y-1">
        <p>💡 <strong>Tip:</strong> The visual editor will automatically generate form fields based on your JSON structure.</p>
        <p>🎨 Supported field types: text, number, boolean, color (keys containing 'color'), URL (keys containing 'url'), and arrays.</p>
      </div>
    </div>
  );
};

export default JsonEditor;
