
import React, { useState } from 'react';
import FieldEditor from '../FieldEditor';
import ArrayRenderer from './components/ArrayRenderer';
import ObjectRenderer from './components/ObjectRenderer';
import { isComplexValue, createFieldFromValue } from './utils/nestedUtils';

interface NestedObjectEditorProps {
  parentKey: string;
  nestedData: Record<string, any>;
  onChange: (key: string, value: any) => void;
  level?: number;
}

const NestedObjectEditor = ({ parentKey, nestedData, onChange, level = 0 }: NestedObjectEditorProps) => {
  const [openStates, setOpenStates] = useState<Record<string, boolean>>({});

  const toggleOpen = (key: string) => {
    setOpenStates(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const renderContent = () => {
    return Object.entries(nestedData).map(([key, value]) => {
      const fullPath = parentKey ? `${parentKey}.${key}` : key;
      const isOpen = openStates[key] ?? true;

      if (Array.isArray(value)) {
        return (
          <ArrayRenderer
            key={key}
            itemKey={key}
            arrayValue={value}
            fullPath={fullPath}
            isOpen={isOpen}
            onToggle={() => toggleOpen(key)}
            onChange={onChange}
            level={level}
          />
        );
      } else if (isComplexValue(value)) {
        return (
          <ObjectRenderer
            key={key}
            itemKey={key}
            value={value}
            fullPath={fullPath}
            isOpen={isOpen}
            onToggle={() => toggleOpen(key)}
            onChange={onChange}
            level={level}
          />
        );
      } else {
        const field = createFieldFromValue(key, value, fullPath);
        return (
          <div key={key} className={`${level > 0 ? 'ml-2' : ''} mt-3`}>
            <FieldEditor
              field={field}
              value={value}
              onChange={(newValue) => onChange(key, newValue)}
            />
          </div>
        );
      }
    });
  };

  return (
    <div className="space-y-2">
      {renderContent()}
    </div>
  );
};

export default NestedObjectEditor;
