
import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ChevronRight, ChevronDown } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import FieldEditor from '../../FieldEditor';
import NestedObjectEditor from '../NestedObjectEditor';
import { isComplexValue, createFieldFromValue } from '../utils/nestedUtils';

interface ArrayRendererProps {
  itemKey: string;
  arrayValue: any[];
  fullPath: string;
  isOpen: boolean;
  onToggle: () => void;
  onChange: (key: string, value: any) => void;
  level: number;
}

const ArrayRenderer = ({ itemKey, arrayValue, fullPath, isOpen, onToggle, onChange, level }: ArrayRendererProps) => {
  const renderArrayItems = () => {
    return arrayValue.map((item, index) => {
      const arrayItemKey = `${itemKey}[${index}]`;
      const arrayItemPath = `${fullPath}[${index}]`;

      if (isComplexValue(item)) {
        return (
          <div key={arrayItemKey} className={`${level > 0 ? 'ml-2' : ''} mt-2`}>
            <Card className="border border-blue-200 bg-blue-50/30">
              <CardHeader className="py-2">
                <CardTitle className="text-sm font-medium text-blue-900">
                  {itemKey} Item {index + 1}
                </CardTitle>
                <Badge variant="outline" className="text-xs bg-blue-100 text-blue-700 w-fit">
                  {Array.isArray(item) ? `${item.length} items` : `${Object.keys(item || {}).length} fields`}
                </Badge>
              </CardHeader>
              <CardContent className="pt-0 pb-3">
                <NestedObjectEditor
                  parentKey={arrayItemPath}
                  nestedData={item}
                  onChange={(nestedKey, nestedValue) => {
                    const updatedItem = Array.isArray(item) 
                      ? [...item]
                      : { ...item, [nestedKey]: nestedValue };
                    
                    if (!Array.isArray(item)) {
                      updatedItem[nestedKey] = nestedValue;
                    }
                    
                    const updatedArray = [...arrayValue];
                    updatedArray[index] = updatedItem;
                    onChange(itemKey, updatedArray);
                  }}
                  level={level + 1}
                />
              </CardContent>
            </Card>
          </div>
        );
      } else {
        const field = createFieldFromValue(`${itemKey}[${index}]`, item, arrayItemPath);
        return (
          <div key={arrayItemKey} className={`${level > 0 ? 'ml-2' : ''} mt-2`}>
            <FieldEditor
              field={field}
              value={item}
              onChange={(newValue) => {
                const updatedArray = [...arrayValue];
                updatedArray[index] = newValue;
                onChange(itemKey, updatedArray);
              }}
            />
          </div>
        );
      }
    });
  };

  return (
    <div className={`${level > 0 ? 'ml-2' : ''} mt-3`}>
      <Collapsible open={isOpen} onOpenChange={onToggle}>
        <Card className="border border-green-200 bg-green-50/30">
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-green-50/50 transition-colors py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {isOpen ? (
                    <ChevronDown className="w-4 h-4 text-green-600" />
                  ) : (
                    <ChevronRight className="w-4 h-4 text-green-600" />
                  )}
                  <CardTitle className="text-sm font-medium text-green-900">
                    {itemKey.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} (Array)
                  </CardTitle>
                  <Badge variant="outline" className="text-xs bg-green-100 text-green-700">
                    {arrayValue.length} item{arrayValue.length !== 1 ? 's' : ''}
                  </Badge>
                </div>
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-0 pb-4">
              {renderArrayItems()}
            </CardContent>
          </CollapsibleContent>
        </Card>
      </Collapsible>
    </div>
  );
};

export default ArrayRenderer;
