
import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ChevronRight, ChevronDown } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import NestedObjectEditor from '../NestedObjectEditor';

interface ObjectRendererProps {
  itemKey: string;
  value: any;
  fullPath: string;
  isOpen: boolean;
  onToggle: () => void;
  onChange: (key: string, value: any) => void;
  level: number;
}

const ObjectRenderer = ({ itemKey, value, fullPath, isOpen, onToggle, onChange, level }: ObjectRendererProps) => {
  return (
    <div className={`${level > 0 ? 'ml-2' : ''} mt-3`}>
      <Collapsible open={isOpen} onOpenChange={onToggle}>
        <Card className="border border-purple-200 bg-purple-50/30">
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-purple-50/50 transition-colors py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {isOpen ? (
                    <ChevronDown className="w-4 h-4 text-purple-600" />
                  ) : (
                    <ChevronRight className="w-4 h-4 text-purple-600" />
                  )}
                  <CardTitle className="text-sm font-medium text-purple-900">
                    {itemKey.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </CardTitle>
                  <Badge variant="outline" className="text-xs bg-purple-100 text-purple-700">
                    {Object.keys(value).length} field{Object.keys(value).length !== 1 ? 's' : ''}
                  </Badge>
                </div>
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-0 pb-4">
              <NestedObjectEditor
                parentKey={fullPath}
                nestedData={value}
                onChange={(nestedKey, nestedValue) => {
                  const updatedValue = { ...value, [nestedKey]: nestedValue };
                  onChange(itemKey, updatedValue);
                }}
                level={level + 1}
              />
            </CardContent>
          </CollapsibleContent>
        </Card>
      </Collapsible>
    </div>
  );
};

export default ObjectRenderer;
