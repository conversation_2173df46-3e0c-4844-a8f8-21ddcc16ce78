
import { ConfigField } from '@/contexts/ConfigContext';

export const isComplexValue = (value: any): boolean => {
  if (value === null || value === undefined) return false;
  
  if (Array.isArray(value)) {
    return value.length > 0;
  }
  
  if (typeof value === 'object') {
    return Object.keys(value).length > 0;
  }
  
  return false;
};

export const createFieldFromValue = (key: string, value: any, fullPath: string): ConfigField => {
  let fieldType: ConfigField['type'] = 'text';
  
  if (typeof value === 'boolean') {
    fieldType = 'toggle';
  } else if (typeof value === 'number') {
    fieldType = 'number';
  } else if (key.toLowerCase().includes('color') || (typeof value === 'string' && value.startsWith('#'))) {
    fieldType = 'color';
  } else if (key.toLowerCase().includes('url') || key.toLowerCase().includes('uri') || key.toLowerCase().includes('link')) {
    fieldType = 'url';
  } else if (key.toLowerCase().includes('image') || key.toLowerCase().includes('img')) {
    fieldType = 'image';
  }

  return {
    id: `nested-${key}`,
    key: key,
    label: key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
    type: fieldType,
    value: value,
    required: false,
    tooltip: `Configure ${key} (Path: ${fullPath})`
  };
};
