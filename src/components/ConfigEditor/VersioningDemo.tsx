
import React, { useState } from 'react';
import { Play, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { runDocumentVersioningDemo } from '@/services/documentVersioningDemo';

const VersioningDemo = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [hasRun, setHasRun] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleRunDemo = async () => {
    setIsRunning(true);
    setHasRun(false);
    
    try {
      await runDocumentVersioningDemo();
      setSuccess(true);
      setHasRun(true);
    } catch (error) {
      console.error('Demo failed:', error);
      setSuccess(false);
      setHasRun(true);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Play className="w-5 h-5" />
          <span>Document Versioning Demo</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm text-gray-600">
          This demo shows the document versioning system in action:
        </div>
        
        <ul className="text-sm space-y-2 ml-4">
          <li>• Create a new document with initial version (v1)</li>
          <li>• Update the document creating a new version (v2)</li>
          <li>• Retrieve version history</li>
          <li>• Access specific versions</li>
          <li>• Show metadata and change tracking</li>
        </ul>
        
        <div className="flex items-center space-x-4">
          <Button 
            onClick={handleRunDemo}
            disabled={isRunning}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Play className="w-4 h-4 mr-2" />
            {isRunning ? 'Running Demo...' : 'Run Demo'}
          </Button>
          
          {hasRun && (
            <div className="flex items-center space-x-2">
              {success ? (
                <>
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-sm text-green-600">Demo completed successfully!</span>
                </>
              ) : (
                <>
                  <XCircle className="w-5 h-5 text-red-500" />
                  <span className="text-sm text-red-600">Demo failed. Check console for details.</span>
                </>
              )}
            </div>
          )}
        </div>
        
        <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded">
          <strong>Note:</strong> This demo simulates AWS S3 operations. In a real implementation, 
          you would need to configure actual AWS credentials and S3 bucket access.
          Check the browser console to see the demo output.
        </div>
      </CardContent>
    </Card>
  );
};

export default VersioningDemo;
