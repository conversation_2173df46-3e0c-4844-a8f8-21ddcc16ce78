
import React, { useState, useEffect } from 'react';
import { Clock, User, FileText, Download, History } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { VersionHistory, DocumentMetadata } from '@/types/versioning';
import { DocumentVersioningService } from '@/services/documentVersioningService';
import { S3Service } from '@/services/s3Service';

interface VersioningPanelProps {
  documentId: string;
  documentType: string;
  environment: 'dev' | 'staging' | 'prod';
  onVersionSelect?: (version: number) => void;
}

const VersioningPanel = ({ 
  documentId, 
  documentType, 
  environment, 
  onVersionSelect 
}: VersioningPanelProps) => {
  const [versionHistory, setVersionHistory] = useState<VersionHistory | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedVersion, setSelectedVersion] = useState<number | null>(null);

  useEffect(() => {
    loadVersionHistory();
  }, [documentId, documentType, environment]);

  const loadVersionHistory = async () => {
    try {
      setLoading(true);
      
      // Initialize versioning service
      const s3Config = {
        bucketName: 'my-config-documents',
        region: 'us-east-1',
        accessKeyId: 'demo-access-key',
        secretAccessKey: 'demo-secret-key'
      };
      
      const s3Service = new S3Service(s3Config);
      const versioningService = new DocumentVersioningService(s3Service);
      
      const history = await versioningService.getVersionHistory(
        documentId,
        documentType,
        environment
      );
      
      setVersionHistory(history);
      if (history && history.versions.length > 0) {
        setSelectedVersion(history.latestVersion);
      }
    } catch (error) {
      console.error('Failed to load version history:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleVersionSelect = (version: number) => {
    setSelectedVersion(version);
    onVersionSelect?.(version);
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const getVersionBadgeVariant = (version: DocumentMetadata) => {
    if (version.version === versionHistory?.latestVersion) {
      return 'default';
    }
    return 'secondary';
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <History className="w-5 h-5" />
            <span>Version History</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <div className="text-sm text-gray-500">Loading version history...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!versionHistory || versionHistory.versions.length === 0) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <History className="w-5 h-5" />
            <span>Version History</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <FileText className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <div className="text-sm text-gray-500">No version history available</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <History className="w-5 h-5" />
            <span>Version History</span>
          </div>
          <Badge variant="outline">
            {versionHistory.versions.length} version{versionHistory.versions.length !== 1 ? 's' : ''}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          {versionHistory.versions.map((version, index) => (
            <div key={version.version}>
              <div 
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedVersion === version.version 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleVersionSelect(version.version)}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <Badge variant={getVersionBadgeVariant(version)}>
                      v{version.version}
                    </Badge>
                    {version.version === versionHistory.latestVersion && (
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        Latest
                      </Badge>
                    )}
                  </div>
                  <div className="text-xs text-gray-500">
                    {Math.round(version.fileSize / 1024 * 100) / 100} KB
                  </div>
                </div>
                
                <div className="space-y-1">
                  <div className="text-sm font-medium">
                    {version.changeDescription || 'No description'}
                  </div>
                  
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <div className="flex items-center space-x-1">
                      <User className="w-3 h-3" />
                      <span>{version.author}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>{formatTimestamp(version.timestamp)}</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end mt-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Handle download/export functionality
                      console.log('Download version', version.version);
                    }}
                  >
                    <Download className="w-3 h-3 mr-1" />
                    Export
                  </Button>
                </div>
              </div>
              
              {index < versionHistory.versions.length - 1 && (
                <Separator className="my-2" />
              )}
            </div>
          ))}
        </div>
        
        <div className="pt-4 border-t">
          <div className="text-xs text-gray-500 space-y-1">
            <div>Document ID: {versionHistory.documentId}</div>
            <div>Type: {versionHistory.documentType}</div>
            <div>Environment: {versionHistory.environment.toUpperCase()}</div>
            <div>Latest Version: v{versionHistory.latestVersion}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default VersioningPanel;
