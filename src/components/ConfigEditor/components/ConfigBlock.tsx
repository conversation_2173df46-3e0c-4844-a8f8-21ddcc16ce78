
import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import NestedObjectEditor from '../NestedObjectEditor';
import SimpleFieldsRenderer from './SimpleFieldsRenderer';
import { isComplexBlock } from '../utils/fieldUtils';

interface ConfigBlockProps {
  blockKey: string;
  blockData: any;
  onUpdate: (blockKey: string, fieldKey: string, value: any) => void;
  onNestedUpdate: (blockKey: string, value: any) => void;
  isOpen: boolean;
  onToggle: () => void;
}

const ConfigBlock = ({ blockKey, blockData, onUpdate, onNestedUpdate, isOpen, onToggle }: ConfigBlockProps) => {
  const isComplex = isComplexBlock(blockData);

  const handleFieldUpdate = (fieldKey: string, value: any) => {
    onUpdate(blockKey, fieldKey, value);
  };

  const handleNestedChange = (key: string, value: any) => {
    if (key === blockKey) {
      onNestedUpdate(blockKey, value);
    } else {
      onUpdate(blockKey, key, value);
    }
  };

  return (
    <Card className="w-full">
      <Collapsible open={isOpen} onOpenChange={onToggle}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {isOpen ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )}
                <CardTitle className="text-lg font-semibold">
                  {blockKey.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </CardTitle>
              </div>
              <Badge variant="outline" className="text-xs">
                {isComplex ? 'Nested Structure' : 'Simple Fields'}
              </Badge>
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        
        <CollapsibleContent>
          <CardContent className="space-y-4">
            {isComplex ? (
              <NestedObjectEditor
                parentKey={blockKey}
                nestedData={blockData}
                onChange={handleNestedChange}
              />
            ) : (
              <SimpleFieldsRenderer
                blockKey={blockKey}
                blockData={blockData}
                onFieldUpdate={handleFieldUpdate}
              />
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

export default ConfigBlock;
