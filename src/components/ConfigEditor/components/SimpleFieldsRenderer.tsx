
import React from 'react';
import { ConfigField } from '@/contexts/ConfigContext';
import FieldEditor from '../FieldEditor';
import { createFieldFromValue } from '../utils/fieldUtils';

interface SimpleFieldsRendererProps {
  blockKey: string;
  blockData: Record<string, any>;
  onFieldUpdate: (fieldKey: string, value: any) => void;
}

const SimpleFieldsRenderer = ({ blockKey, blockData, onFieldUpdate }: SimpleFieldsRendererProps) => {
  return (
    <>
      {Object.entries(blockData).map(([fieldKey, fieldValue]) => {
        const field = createFieldFromValue(blockKey, fieldKey, fieldValue);
        
        // Enhanced debug logging to see what field type is being created
        console.log(`=== FIELD DEBUG ===`);
        console.log(`Block: ${blockKey}, Field: ${fieldKey}, Value: ${fieldValue}`);
        console.log(`Detected Type: ${field.type}`);
        console.log(`Field Key Lower: ${fieldKey.toLowerCase()}`);
        console.log(`Contains 'image': ${fieldKey.toLowerCase().includes('image')}`);
        console.log(`Contains 'uri': ${fieldKey.toLowerCase().includes('uri')}`);
        console.log(`==================`);
        
        return (
          <FieldEditor
            key={fieldKey}
            field={field}
            value={fieldValue}
            onChange={(value) => onFieldUpdate(fieldKey, value)}
          />
        );
      })}
    </>
  );
};

export default SimpleFieldsRenderer;
