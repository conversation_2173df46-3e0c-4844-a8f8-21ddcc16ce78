
import { ConfigField } from '@/contexts/ConfigContext';

export const isComplexBlock = (blockData: any): boolean => {
  return Object.values(blockData).some(value => 
    Array.isArray(value) || (typeof value === 'object' && value !== null)
  );
};

const isImageField = (key: string, value: any): boolean => {
  const lowerKey = key.toLowerCase();
  
  // Comprehensive image-related keywords and patterns
  const imageKeywords = [
    'image', 'img', 'photo', 'picture', 'avatar', 'banner', 'icon', 'logo',
    'bg', 'background', 'header', 'badge', 'ring', 'card', 'screen'
  ];
  
  // Check for direct image keywords
  const hasImageKeyword = imageKeywords.some(keyword => lowerKey.includes(keyword));
  
  // Check for image-related URI/URL patterns
  const imageUriPatterns = [
    'imageuri', 'imageurl', 'imguri', 'imgurl',
    'bguri', 'bgurl', 'backgrounduri', 'backgroundurl',
    'headeruri', 'headerurl', 'avataruri', 'avatarurl',
    'iconuri', 'iconurl', 'logoUri', 'logourl'
  ];
  
  const hasImageUriPattern = imageUriPatterns.some(pattern => lowerKey.includes(pattern));
  
  // Check for compound patterns like "cardBGImage", "winningAmountBGImageUri"
  const hasCompoundImagePattern = (
    (lowerKey.includes('bg') || lowerKey.includes('background')) && 
    (lowerKey.includes('image') || lowerKey.includes('uri') || lowerKey.includes('url'))
  ) || (
    lowerKey.includes('image') && 
    (lowerKey.includes('uri') || lowerKey.includes('url'))
  );
  
  // Check if value looks like an image URL
  const isImageUrl = typeof value === 'string' && (
    !!value.match(/\.(jpg|jpeg|png|gif|webp|svg)(\?.*)?$/i) ||
    value.includes('image') ||
    value.includes('photo')
  );
  
  const isImageFieldResult = hasImageKeyword || hasImageUriPattern || hasCompoundImagePattern || isImageUrl;
  
  console.log(`Checking image field for ${key}:`);
  console.log(`  - hasImageKeyword: ${hasImageKeyword}`);
  console.log(`  - hasImageUriPattern: ${hasImageUriPattern}`);
  console.log(`  - hasCompoundImagePattern: ${hasCompoundImagePattern}`);
  console.log(`  - isImageUrl: ${isImageUrl}`);
  console.log(`  - Final result: ${isImageFieldResult}`);
  
  return isImageFieldResult;
};

export const createFieldFromValue = (blockKey: string, fieldKey: string, fieldValue: any): ConfigField => {
  let fieldType: ConfigField['type'] = 'text';

  // Auto-detect field type with improved image detection
  if (typeof fieldValue === 'boolean') {
    fieldType = 'toggle';
  } else if (typeof fieldValue === 'number') {
    fieldType = 'number';
  } else if (fieldKey.toLowerCase().includes('color') || (typeof fieldValue === 'string' && fieldValue.startsWith('#'))) {
    fieldType = 'color';
  } else if (isImageField(fieldKey, fieldValue)) {
    fieldType = 'image';
    console.log(`✅ Setting field type to 'image' for ${fieldKey}`);
  } else if (fieldKey.toLowerCase().includes('url') || fieldKey.toLowerCase().includes('uri') || fieldKey.toLowerCase().includes('link')) {
    fieldType = 'url';
  } else if (typeof fieldValue === 'object' && fieldValue !== null) {
    fieldType = 'textarea';
  }

  return {
    id: `${blockKey}-${fieldKey}`,
    key: fieldKey,
    label: fieldKey.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
    type: fieldType,
    value: fieldValue,
    required: false,
    tooltip: `Configure ${fieldKey}`
  };
};
