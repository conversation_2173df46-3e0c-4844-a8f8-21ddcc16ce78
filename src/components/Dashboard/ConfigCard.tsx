
import React, { useState, useEffect } from 'react';
import { Edit, Trash2, Co<PERSON>, Clock, Upload, CheckCircle, AlertCircle, ExternalLink, Link } from 'lucide-react';
import { Configuration } from '@/contexts/ConfigContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PromotionService, PromotionCandidate } from '@/services/promotionService';
import { useConfig } from '@/contexts/ConfigContext';
import { toast } from '@/hooks/use-toast';

interface ConfigCardProps {
  config: Configuration;
  onEdit: (config: Configuration) => void;
  onDelete: (id: string) => void;
  onDuplicate: (config: Configuration) => void;
}

const ConfigCard = ({ config, onEdit, onDelete, onDuplicate }: ConfigCardProps) => {
  const { s3Config, loadConfigurations } = useConfig();
  const [promotionStatus, setPromotionStatus] = useState<{
    canPromoteToStaging: boolean;
    canPromoteToProduction: boolean;
    hasChangesToStaging: boolean;
    hasChangesToProduction: boolean;
    promotedConfigUrl?: string;
  }>({
    canPromoteToStaging: false,
    canPromoteToProduction: false,
    hasChangesToStaging: false,
    hasChangesToProduction: false
  });
  const [isPromoting, setIsPromoting] = useState(false);

  useEffect(() => {
    if (s3Config) {
      checkPromotionStatus();
    }
  }, [config, s3Config]);

  const checkPromotionStatus = async () => {
    if (!s3Config) return;
    
    const promotionService = new PromotionService(s3Config);
    
    try {
      if (config.environment === 'dev') {
        const stagingCandidates = await promotionService.getPromotionCandidates('dev');
        const candidate = stagingCandidates.find(c => c.name === config.name);
        
        setPromotionStatus(prev => ({
          ...prev,
          canPromoteToStaging: true,
          hasChangesToStaging: candidate?.hasChanges || false,
          promotedConfigUrl: candidate?.promotedConfigUrl
        }));
      }
      
      if (config.environment === 'dev' || config.environment === 'staging') {
        const targetEnv = config.environment === 'dev' ? 'staging' : 'prod';
        const prodCandidates = await promotionService.getPromotionCandidates(targetEnv === 'prod' ? 'staging' : 'dev');
        const candidate = prodCandidates.find(c => c.name === config.name);
        
        if (config.environment === 'staging') {
          setPromotionStatus(prev => ({
            ...prev,
            canPromoteToProduction: true,
            hasChangesToProduction: candidate?.hasChanges || false,
            promotedConfigUrl: candidate?.promotedConfigUrl
          }));
        }
      }
    } catch (error) {
      console.error('Error checking promotion status:', error);
    }
  };

  const handlePromote = async (targetEnvironment: 'staging' | 'prod') => {
    if (!s3Config) {
      toast({
        title: "S3 Not Connected",
        description: "Please connect to S3 first to enable promotions.",
        variant: "destructive"
      });
      return;
    }

    setIsPromoting(true);
    const promotionService = new PromotionService(s3Config);
    
    try {
      const sourceEnv = config.environment as 'dev' | 'staging';
      const candidates = await promotionService.getPromotionCandidates(sourceEnv);
      const candidate = candidates.find(c => c.name === config.name);
      
      if (!candidate) {
        toast({
          title: "Promotion Failed",
          description: "Configuration not found for promotion.",
          variant: "destructive"
        });
        return;
      }

      const success = await promotionService.promoteConfiguration(candidate);
      
      if (success) {
        toast({
          title: "Configuration Promoted",
          description: `${config.name} has been promoted to ${targetEnvironment}.`,
        });
        
        // Refresh configurations and promotion status
        await loadConfigurations();
        await checkPromotionStatus();
      } else {
        toast({
          title: "Promotion Failed",
          description: "Failed to promote configuration.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Promotion error:', error);
      toast({
        title: "Promotion Error",
        description: "An error occurred during promotion.",
        variant: "destructive"
      });
    } finally {
      setIsPromoting(false);
    }
  };

  const copyUrlToClipboard = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      toast({
        title: "URL Copied",
        description: "Promoted config URL copied to clipboard.",
      });
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy URL to clipboard.",
        variant: "destructive"
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEnvironmentColor = (env: string) => {
    switch (env) {
      case 'prod':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'staging':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'dev':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPromotionStatusIcon = (hasChanges: boolean) => {
    return hasChanges ? (
      <AlertCircle className="w-3 h-3 text-orange-500" />
    ) : (
      <CheckCircle className="w-3 h-3 text-green-500" />
    );
  };

  return (
    <Card className="group hover:shadow-lg transition-all duration-200 hover:border-blue-200">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {config.name}
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Badge className={`text-xs font-medium ${getEnvironmentColor(config.environment)}`}>
                {config.environment.toUpperCase()}
              </Badge>
              <Badge variant="outline" className="text-xs">
                v{config.version}
              </Badge>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="py-3">
        <div className="space-y-2">
          <div className="text-sm text-gray-600">
            {Object.keys(config.jsonData).length} configuration block{Object.keys(config.jsonData).length !== 1 ? 's' : ''}
          </div>
          <div className="flex items-center text-xs text-gray-500">
            <Clock className="w-3 h-3 mr-1" />
            Modified {formatDate(config.lastModified)}
          </div>
          
          {/* Promoted Config URL Display */}
          {promotionStatus.promotedConfigUrl && (config.environment === 'staging' || config.environment === 'prod') && (
            <div className="pt-2 border-t border-gray-100">
              <div className="text-xs text-gray-500 mb-1">Promoted Config URL:</div>
              <div className="flex items-center space-x-1 bg-gray-50 p-2 rounded text-xs">
                <Link className="w-3 h-3 text-gray-400" />
                <code className="flex-1 text-gray-700 break-all">{promotionStatus.promotedConfigUrl}</code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyUrlToClipboard(promotionStatus.promotedConfigUrl!)}
                  className="h-6 w-6 p-0 hover:bg-gray-200"
                >
                  <Copy className="w-3 h-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => window.open(promotionStatus.promotedConfigUrl, '_blank')}
                  className="h-6 w-6 p-0 hover:bg-gray-200"
                >
                  <ExternalLink className="w-3 h-3" />
                </Button>
              </div>
            </div>
          )}
          
          {/* Promotion Status Indicators */}
          {(promotionStatus.canPromoteToStaging || promotionStatus.canPromoteToProduction) && (
            <div className="pt-2 border-t border-gray-100">
              <div className="text-xs text-gray-500 mb-1">Promotion Status:</div>
              <div className="space-y-1">
                {promotionStatus.canPromoteToStaging && (
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-600">To Staging:</span>
                    <div className="flex items-center space-x-1">
                      {getPromotionStatusIcon(promotionStatus.hasChangesToStaging)}
                      <span className={promotionStatus.hasChangesToStaging ? 'text-orange-600' : 'text-green-600'}>
                        {promotionStatus.hasChangesToStaging ? 'Has Changes' : 'Up to Date'}
                      </span>
                    </div>
                  </div>
                )}
                {promotionStatus.canPromoteToProduction && (
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-600">To Production:</span>
                    <div className="flex items-center space-x-1">
                      {getPromotionStatusIcon(promotionStatus.hasChangesToProduction)}
                      <span className={promotionStatus.hasChangesToProduction ? 'text-orange-600' : 'text-green-600'}>
                        {promotionStatus.hasChangesToProduction ? 'Has Changes' : 'Up to Date'}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter className="pt-3 border-t border-gray-100">
        <div className="w-full space-y-2">
          {/* Main Actions */}
          <div className="flex items-center justify-between">
            <Button
              variant="default"
              size="sm"
              onClick={() => onEdit(config)}
              className="flex-1 mr-2"
            >
              <Edit className="w-4 h-4 mr-1" />
              Edit
            </Button>
            <div className="flex space-x-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDuplicate(config)}
              >
                <Copy className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(config.id)}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          {/* Promotion Actions - Only show if S3 is connected */}
          {s3Config && (promotionStatus.canPromoteToStaging || promotionStatus.canPromoteToProduction) && (
            <div className="flex space-x-2">
              {promotionStatus.canPromoteToStaging && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePromote('staging')}
                  disabled={isPromoting}
                  className="flex-1 border-green-200 text-green-700 hover:bg-green-50"
                >
                  <Upload className="w-3 h-3 mr-1" />
                  {isPromoting ? 'Promoting...' : 'To Staging'}
                </Button>
              )}
              {promotionStatus.canPromoteToProduction && config.environment === 'staging' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePromote('prod')}
                  disabled={isPromoting}
                  className="flex-1 border-red-200 text-red-700 hover:bg-red-50"
                >
                  <Upload className="w-3 h-3 mr-1" />
                  {isPromoting ? 'Promoting...' : 'To Production'}
                </Button>
              )}
            </div>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};

export default ConfigCard;
