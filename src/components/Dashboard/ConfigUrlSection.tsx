
import React from 'react';
import { <PERSON><PERSON>, ExternalLink, Link } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useConfig } from '@/contexts/ConfigContext';
import { toast } from '@/hooks/use-toast';

interface ConfigUrlSectionProps {
  selectedEnvironment: 'dev' | 'staging' | 'prod';
}

const ConfigUrlSection = ({ selectedEnvironment }: ConfigUrlSectionProps) => {
  const { configurations, s3Config } = useConfig();

  const environmentConfigs = configurations.filter(
    config => config.environment === selectedEnvironment
  );

  const generateConfigUrl = (configName: string, environment: string): string => {
    if (s3Config) {
      // Generate S3 URL for the configuration
      const bucketName = s3Config.bucketName;
      const key = `promoted-configs/${environment}/${configName}.json`;
      return `https://${bucketName}.s3.amazonaws.com/${key}`;
    } else {
      // Fallback URL when S3 is not configured
      return `${window.location.origin}/api/configs/${environment}/${configName}`;
    }
  };

  const copyUrlToClipboard = async (url: string, configName: string) => {
    try {
      await navigator.clipboard.writeText(url);
      toast({
        title: "URL Copied",
        description: `URL for ${configName} copied to clipboard.`,
      });
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy URL to clipboard.",
        variant: "destructive"
      });
    }
  };

  const getEnvironmentColor = (env: string) => {
    switch (env) {
      case 'prod':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'staging':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'dev':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (environmentConfigs.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center space-x-2">
            <Link className="w-5 h-5" />
            <span>Configuration URLs</span>
            <Badge className={`text-xs font-medium ${getEnvironmentColor(selectedEnvironment)}`}>
              {selectedEnvironment.toUpperCase()}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500 text-sm">
            No configurations found for {selectedEnvironment} environment.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center space-x-2">
          <Link className="w-5 h-5" />
          <span>Configuration URLs</span>
          <Badge className={`text-xs font-medium ${getEnvironmentColor(selectedEnvironment)}`}>
            {selectedEnvironment.toUpperCase()}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {!s3Config && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-sm">
            <p className="text-yellow-800">
              <strong>Note:</strong> S3 is not configured. URLs shown are placeholder endpoints.
              Connect to S3 for actual configuration URLs.
            </p>
          </div>
        )}
        
        {environmentConfigs.map((config) => {
          const configUrl = generateConfigUrl(config.name, selectedEnvironment);
          
          return (
            <div key={config.id} className="border border-gray-200 rounded-lg p-3 space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-900">{config.name}</h4>
                <Badge variant="outline" className="text-xs">
                  v{config.version}
                </Badge>
              </div>
              
              <div className="flex items-center space-x-2 bg-gray-50 p-2 rounded text-sm">
                <code className="flex-1 text-gray-700 break-all">{configUrl}</code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyUrlToClipboard(configUrl, config.name)}
                  className="h-8 w-8 p-0 hover:bg-gray-200"
                >
                  <Copy className="w-4 h-4" />
                </Button>
                {s3Config && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => window.open(configUrl, '_blank')}
                    className="h-8 w-8 p-0 hover:bg-gray-200"
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
};

export default ConfigUrlSection;
