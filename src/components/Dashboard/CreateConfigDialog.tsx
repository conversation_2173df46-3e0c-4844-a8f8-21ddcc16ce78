
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useConfig } from '@/contexts/ConfigContext';
import { toast } from '@/hooks/use-toast';

interface CreateConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const CreateConfigDialog = ({ open, onOpenChange }: CreateConfigDialogProps) => {
  const { currentEnvironment, addConfiguration, isLoading } = useConfig();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    jsonData: ''
  });
  const [isCreating, setIsCreating] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsCreating(true);
      const parsedJson = JSON.parse(formData.jsonData || '{}');
      
      const success = await addConfiguration({
        name: formData.name,
        environment: currentEnvironment,
        blocks: [],
        jsonData: parsedJson
      });

      if (success) {
        setFormData({ name: '', description: '', jsonData: '' });
        onOpenChange(false);
      }
    } catch (error) {
      toast({
        title: "Invalid JSON",
        description: "Please provide valid JSON data.",
        variant: "destructive"
      });
    } finally {
      setIsCreating(false);
    }
  };

  const sampleJson = `{
  "winnerPopupModalConfig": {
    "title": "Congratulations!",
    "subtitle": "You won the game",
    "backgroundColor": "#4F46E5",
    "showAnimation": true,
    "buttons": [
      {
        "label": "Play Again",
        "action": "restart",
        "color": "#10B981"
      }
    ]
  }
}`;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Configuration</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Configuration Name</Label>
            <Input
              id="name"
              placeholder="e.g., Winner Popup Modal"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Input
              id="description"
              placeholder="Brief description of this configuration"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="jsonData">JSON Configuration</Label>
            <Textarea
              id="jsonData"
              placeholder={sampleJson}
              value={formData.jsonData}
              onChange={(e) => setFormData(prev => ({ ...prev, jsonData: e.target.value }))}
              className="h-64 font-mono text-sm"
            />
            <p className="text-xs text-gray-500">
              Paste your JSON configuration here. The system will auto-generate form fields based on the structure.
            </p>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isCreating || isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isCreating ? 'Creating...' : 'Create Configuration'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateConfigDialog;
