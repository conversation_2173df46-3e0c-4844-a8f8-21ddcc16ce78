import React, { useState, useEffect } from 'react';
import { Plus, Upload, RefreshCw, Settings } from 'lucide-react';
import { useConfig } from '@/contexts/ConfigContext';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import ConfigCard from './ConfigCard';
import CreateConfigDialog from './CreateConfigDialog';
import S3ConfigDialog from './S3ConfigDialog';
import PromotionDialog from './PromotionDialog';
import ConfigUrlSection from './ConfigUrlSection';
import { PromotionService } from '@/services/promotionService';
import { toast } from '@/hooks/use-toast';

const Dashboard = () => {
  const { 
    configurations, 
    currentEnvironment, 
    setCurrentEnvironment, 
    setSelectedConfig, 
    deleteConfiguration, 
    duplicateConfiguration,
    loadConfigurations,
    isLoading,
    s3Config
  } = useConfig();
  
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isS3DialogOpen, setIsS3DialogOpen] = useState(false);
  const [isPromotionDialogOpen, setIsPromotionDialogOpen] = useState(false);
  const [promotionCandidatesCount, setPromotionCandidatesCount] = useState(0);

  const environmentConfigs = configurations.filter(
    config => config.environment === currentEnvironment
  );

  useEffect(() => {
    if (s3Config) {
      loadPromotionCandidatesCount();
    }
  }, [currentEnvironment, s3Config, configurations]);

  const loadPromotionCandidatesCount = async () => {
    if (!s3Config || currentEnvironment === 'prod') {
      setPromotionCandidatesCount(0);
      return;
    }
    
    try {
      const promotionService = new PromotionService(s3Config);
      const candidates = await promotionService.getPromotionCandidates(currentEnvironment as 'dev' | 'staging');
      const candidatesWithChanges = candidates.filter(c => c.hasChanges);
      setPromotionCandidatesCount(candidatesWithChanges.length);
    } catch (error) {
      console.error('Error loading promotion candidates:', error);
      setPromotionCandidatesCount(0);
    }
  };

  const handleRefresh = async () => {
    await loadConfigurations();
    if (s3Config) {
      await loadPromotionCandidatesCount();
    }
    toast({
      title: "Refreshed",
      description: "Configurations have been refreshed.",
    });
  };

  const handleDuplicate = async (config: any) => {
    try {
      await duplicateConfiguration(config);
      toast({
        title: "Configuration Duplicated",
        description: `${config.name} has been duplicated successfully.`,
      });
    } catch (error) {
      toast({
        title: "Duplicate Failed",
        description: "Failed to duplicate configuration.",
        variant: "destructive"
      });
    }
  };

  const handleDelete = async (id: string) => {
    const config = configurations.find(c => c.id === id);
    if (!config) return;

    if (window.confirm(`Are you sure you want to delete "${config.name}"?`)) {
      try {
        await deleteConfiguration(id);
        toast({
          title: "Configuration Deleted",
          description: `${config.name} has been deleted.`,
        });
      } catch (error) {
        toast({
          title: "Delete Failed",
          description: "Failed to delete configuration.",
          variant: "destructive"
        });
      }
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Configuration Management</h1>
          <p className="text-gray-600">Manage your application configurations across environments</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsS3DialogOpen(true)}
          >
            <Settings className="w-4 h-4 mr-2" />
            S3 Config
          </Button>
          
          {s3Config && promotionCandidatesCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsPromotionDialogOpen(true)}
              className="border-orange-200 text-orange-700 hover:bg-orange-50"
            >
              <Upload className="w-4 h-4 mr-2" />
              Promotions ({promotionCandidatesCount})
            </Button>
          )}
        </div>
      </div>

      {/* Environment Selector and Actions */}
      <div className="flex items-center justify-between">
        <Select value={currentEnvironment} onValueChange={setCurrentEnvironment}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Select environment" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="dev">Development</SelectItem>
            <SelectItem value="staging">Staging</SelectItem>
            <SelectItem value="prod">Production</SelectItem>
          </SelectContent>
        </Select>

        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Create Configuration
        </Button>
      </div>

      {/* Configuration URLs Section */}
      <ConfigUrlSection selectedEnvironment={currentEnvironment} />

      {/* Configurations Grid */}
      {environmentConfigs.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-4">No configurations found</div>
          <p className="text-gray-500 mb-6">
            Create your first configuration for the {currentEnvironment} environment
          </p>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Create Configuration
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {environmentConfigs.map((config) => (
            <ConfigCard
              key={config.id}
              config={config}
              onEdit={setSelectedConfig}
              onDelete={handleDelete}
              onDuplicate={handleDuplicate}
            />
          ))}
        </div>
      )}

      {/* Dialogs */}
      <CreateConfigDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
      />
      
      <S3ConfigDialog
        open={isS3DialogOpen}
        onOpenChange={setIsS3DialogOpen}
      />
      
      <PromotionDialog
        open={isPromotionDialogOpen}
        onOpenChange={setIsPromotionDialogOpen}
        environment={currentEnvironment as 'dev' | 'staging'}
      />
    </div>
  );
};

export default Dashboard;
