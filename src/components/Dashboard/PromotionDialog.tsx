
import React, { useState, useEffect } from 'react';
import { ArrowR<PERSON>, Eye, Upload, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { PromotionService, PromotionCandidate, PromotionDiff } from '@/services/promotionService';
import { useConfig } from '@/contexts/ConfigContext';
import { toast } from '@/hooks/use-toast';

interface PromotionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  environment: 'dev' | 'staging';
}

const PromotionDialog = ({ open, onOpenChange, environment }: PromotionDialogProps) => {
  const { isS3Connected, loadConfigurations } = useConfig();
  const [candidates, setCandidates] = useState<PromotionCandidate[]>([]);
  const [selectedCandidate, setSelectedCandidate] = useState<PromotionCandidate | null>(null);
  const [diff, setDiff] = useState<PromotionDiff | null>(null);
  const [loading, setLoading] = useState(false);
  const [promoting, setPromoting] = useState(false);

  const targetEnvironment = environment === 'dev' ? 'staging' : 'prod';

  useEffect(() => {
    if (open) {
      loadCandidates();
    }
  }, [open, environment]);

  const loadCandidates = async () => {
    setLoading(true);
    try {
      const promotionService = new PromotionService();
      const candidateList = await promotionService.getPromotionCandidates(environment);
      setCandidates(candidateList);
    } catch (error) {
      console.error('Failed to load promotion candidates:', error);
      toast({
        title: "Load Error",
        description: "Failed to load promotion candidates.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleViewDiff = (candidate: PromotionCandidate) => {
    const promotionService = new PromotionService();
    const diffResult = promotionService.generateDiff(candidate.sourceConfig, candidate.targetConfig);
    setDiff(diffResult);
    setSelectedCandidate(candidate);
  };

  const handlePromote = async (candidate: PromotionCandidate) => {
    setPromoting(true);
    try {
      const promotionService = new PromotionService();
      const success = await promotionService.promoteConfiguration(candidate);
      
      if (success) {
        toast({
          title: "Configuration Promoted",
          description: `"${candidate.name}" has been promoted to ${targetEnvironment}.`
        });
        
        await loadConfigurations();
        await loadCandidates();
        setSelectedCandidate(null);
        setDiff(null);
      } else {
        toast({
          title: "Promotion Failed",
          description: "Failed to promote configuration.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Promotion error:', error);
      toast({
        title: "Promotion Error",
        description: "An error occurred during promotion.",
        variant: "destructive"
      });
    } finally {
      setPromoting(false);
    }
  };

  const renderDiffValue = (value: any) => {
    if (typeof value === 'object') {
      return <pre className="text-xs">{JSON.stringify(value, null, 2)}</pre>;
    }
    return <span className="font-mono">{String(value)}</span>;
  };

  if (selectedCandidate && diff) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Upload className="w-5 h-5" />
              Promote "{selectedCandidate.name}" to {targetEnvironment.toUpperCase()}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <Badge variant="outline">{environment.toUpperCase()}</Badge>
                <ArrowRight className="w-4 h-4" />
                <Badge variant="default">{targetEnvironment.toUpperCase()}</Badge>
              </div>
              <div className="text-sm text-gray-600">
                Version: {selectedCandidate.sourceConfig.version} → {(selectedCandidate.targetConfig?.version || 0) + 1}
              </div>
            </div>

            {Object.keys(diff.added).length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-green-700">Added Properties</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {Object.entries(diff.added).map(([path, value]) => (
                    <div key={path} className="p-2 bg-green-50 rounded border-l-4 border-green-500">
                      <div className="font-mono text-sm font-medium text-green-800">{path}</div>
                      <div className="text-green-700">{renderDiffValue(value)}</div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {Object.keys(diff.modified).length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-blue-700">Modified Properties</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {Object.entries(diff.modified).map(([path, change]) => (
                    <div key={path} className="p-2 bg-blue-50 rounded border-l-4 border-blue-500">
                      <div className="font-mono text-sm font-medium text-blue-800">{path}</div>
                      <div className="grid grid-cols-2 gap-2 mt-1">
                        <div>
                          <div className="text-xs text-red-600 font-medium">From:</div>
                          <div className="text-red-700">{renderDiffValue((change as any).from)}</div>
                        </div>
                        <div>
                          <div className="text-xs text-green-600 font-medium">To:</div>
                          <div className="text-green-700">{renderDiffValue((change as any).to)}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {Object.keys(diff.removed).length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-red-700">Removed Properties</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {Object.entries(diff.removed).map(([path, value]) => (
                    <div key={path} className="p-2 bg-red-50 rounded border-l-4 border-red-500">
                      <div className="font-mono text-sm font-medium text-red-800">{path}</div>
                      <div className="text-red-700">{renderDiffValue(value)}</div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => { setSelectedCandidate(null); setDiff(null); }}>
                Back
              </Button>
              <Button 
                onClick={() => handlePromote(selectedCandidate)}
                disabled={promoting}
                className="bg-green-600 hover:bg-green-700"
              >
                {promoting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Promoting...
                  </>
                ) : (
                  <>
                    <Upload className="w-4 h-4 mr-2" />
                    Confirm Promotion
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Promote to {targetEnvironment.toUpperCase()}
          </DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin mr-2" />
            Loading promotion candidates...
          </div>
        ) : candidates.length > 0 ? (
          <div className="space-y-3">
            {candidates.map((candidate) => (
              <Card key={candidate.id} className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">{candidate.name}</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="outline">{candidate.sourceEnvironment.toUpperCase()}</Badge>
                      <ArrowRight className="w-3 h-3" />
                      <Badge variant="default">{candidate.targetEnvironment.toUpperCase()}</Badge>
                      {candidate.hasChanges && (
                        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                          Has Changes
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleViewDiff(candidate)}
                    >
                      <Eye className="w-3 h-3 mr-1" />
                      View Diff
                    </Button>
                    <Button 
                      size="sm"
                      onClick={() => handlePromote(candidate)}
                      disabled={promoting}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <Upload className="w-3 h-3 mr-1" />
                      Promote
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No configurations available for promotion</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default PromotionDialog;
