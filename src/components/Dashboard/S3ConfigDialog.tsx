
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { useConfig } from '@/contexts/ConfigContext';
import { toast } from '@/hooks/use-toast';
import { Cloud, Database } from 'lucide-react';

interface S3ConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const S3ConfigDialog = ({ open, onOpenChange }: S3ConfigDialogProps) => {
  const { connectS3 } = useConfig();
  const [formData, setFormData] = useState({
    region: 'us-east-1',
    accessKeyId: '',
    secretAccessKey: '',
    bucketName: '',
    endpoint: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.accessKeyId || !formData.secretAccessKey || !formData.bucketName) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    const s3Config = {
      region: formData.region,
      accessKeyId: formData.accessKeyId,
      secretAccessKey: formData.secretAccessKey,
      bucketName: formData.bucketName,
      ...(formData.endpoint && { endpoint: formData.endpoint })
    };

    connectS3(s3Config);
    onOpenChange(false);
    
    // Clear form data after successful submission
    setFormData({
      region: 'us-east-1',
      accessKeyId: '',
      secretAccessKey: '',
      bucketName: '',
      endpoint: ''
    });
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Cloud className="w-5 h-5" />
            Connect to AWS S3
          </DialogTitle>
          <DialogDescription>
            Configure AWS S3 to store your configurations in the cloud instead of local storage.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="region">AWS Region *</Label>
            <Input
              id="region"
              value={formData.region}
              onChange={(e) => handleInputChange('region', e.target.value)}
              placeholder="us-east-1"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="accessKeyId">Access Key ID *</Label>
            <Input
              id="accessKeyId"
              value={formData.accessKeyId}
              onChange={(e) => handleInputChange('accessKeyId', e.target.value)}
              placeholder="AKIA..."
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="secretAccessKey">Secret Access Key *</Label>
            <Input
              id="secretAccessKey"
              type="password"
              value={formData.secretAccessKey}
              onChange={(e) => handleInputChange('secretAccessKey', e.target.value)}
              placeholder="Secret key"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="bucketName">Bucket Name *</Label>
            <Input
              id="bucketName"
              value={formData.bucketName}
              onChange={(e) => handleInputChange('bucketName', e.target.value)}
              placeholder="my-config-bucket"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="endpoint">Custom Endpoint (Optional)</Label>
            <Input
              id="endpoint"
              value={formData.endpoint}
              onChange={(e) => handleInputChange('endpoint', e.target.value)}
              placeholder="https://s3.example.com"
            />
            <p className="text-xs text-gray-500">
              Leave empty for standard AWS S3, or provide a custom endpoint for S3-compatible services
            </p>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
              <Database className="w-4 h-4 mr-2" />
              Connect S3
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default S3ConfigDialog;
