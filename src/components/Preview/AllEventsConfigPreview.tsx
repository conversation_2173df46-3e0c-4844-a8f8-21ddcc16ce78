
import React from 'react';

interface AllEventsConfigPreviewProps {
  config: any;
}

const AllEventsConfigPreview = ({ config }: AllEventsConfigPreviewProps) => {
  // Extract events configuration
  const eventsTitle = config?.title || 'ALL EVENTS';
  const events = config?.events || [
    { name: 'GOAT Championship', date: 'Dec 15, 2024', status: 'Completed' },
    { name: 'Weekly Tournament', date: 'Dec 20, 2024', status: 'Live' },
    { name: 'Special Event', date: 'Dec 25, 2024', status: 'Upcoming' }
  ];
  
  const styling = config?.styling || {};
  const titleColor = styling?.titleColor || '#E9C372';
  const eventNameColor = styling?.eventNameColor || '#FFFFFF';
  const dateColor = styling?.dateColor || '#9CA3AF';
  const statusColors = styling?.statusColors || {
    Completed: '#10B981',
    Live: '#EF4444',
    Upcoming: '#F59E0B'
  };

  return (
    <div className="h-full bg-gradient-to-b from-gray-900 to-black relative overflow-hidden">
      {/* Header */}
      <div className="text-center pt-6 pb-4">
        <h1 
          className="text-xl font-bold"
          style={{ color: titleColor }}
        >
          {eventsTitle}
        </h1>
      </div>
      
      {/* Events List */}
      <div className="flex-1 px-4 overflow-y-auto">
        <div className="space-y-4">
          {events.map((event: any, index: number) => (
            <div key={index} className="bg-gray-800 bg-opacity-50 rounded-lg p-4">
              <div className="flex justify-between items-start mb-2">
                <h3 
                  className="font-semibold"
                  style={{ color: eventNameColor }}
                >
                  {event.name}
                </h3>
                <span 
                  className="text-xs px-2 py-1 rounded-full font-medium"
                  style={{ 
                    backgroundColor: statusColors[event.status] || '#6B7280',
                    color: '#000000'
                  }}
                >
                  {event.status}
                </span>
              </div>
              <p 
                className="text-sm"
                style={{ color: dateColor }}
              >
                {event.date}
              </p>
            </div>
          ))}
        </div>
      </div>
      
      {/* Preview Label */}
      <div className="absolute bottom-4 left-4 right-4 text-center">
        <div className="text-white text-xs opacity-75 bg-black bg-opacity-50 rounded px-2 py-1">
          All Events Config Preview
        </div>
      </div>
    </div>
  );
};

export default AllEventsConfigPreview;
