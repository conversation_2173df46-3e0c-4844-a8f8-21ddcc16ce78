
import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface AllEventsPreviewProps {
  config: any;
}

const AllEventsPreview = ({ config }: AllEventsPreviewProps) => {
  const [expandedDays, setExpandedDays] = useState<Set<number>>(new Set([1])); // Day 1 expanded by default

  // Mock data for events
  const eventData = [
    {
      day: 1,
      date: "23 Feb (Sun)",
      events: [
        { name: "NPS#1 KICKOFF 1CR GTD", buyIn: "5750", time: "2:30 PM" },
        { name: "NPS#2 SLAMDUNK 50L GTD", buyIn: "2875", time: "4:00 PM" },
        { name: "NPS#3 (5-MAX) HYPER 25L GTD", buyIn: "2300", time: "5:30 PM" },
        { name: "NPS#4 (PLO-5) KNOCKOUT 30L GTD (PKO)", buyIn: "2875", time: "6:30 PM" },
        { name: "NPS#5 INAUGURAL 1CR GTD", buyIn: "9200", time: "7:00 PM" },
        { name: "NPS#6 MARATHON 50L GTD", buyIn: "3450", time: "8:30 PM" },
        { name: "NPS#7 (7-MAX) SPRINT 40L GTD", buyIn: "3450", time: "9:30 PM" }
      ]
    },
    {
      day: 2,
      date: "24 Feb (Mon)",
      events: []
    },
    {
      day: 3,
      date: "25 Feb (Tue)",
      events: []
    },
    {
      day: 4,
      date: "26 Feb (Wed)",
      events: []
    }
  ];

  const toggleDay = (day: number) => {
    const newExpanded = new Set(expandedDays);
    if (newExpanded.has(day)) {
      newExpanded.delete(day);
    } else {
      newExpanded.add(day);
    }
    setExpandedDays(newExpanded);
  };

  return (
    <div 
      className="h-full relative overflow-hidden"
      style={{
        backgroundImage: `url(${config?.screenBGImageUri})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundColor: config?.screenBGColor || '#000000'
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 pt-8">
        <button className="text-white">
          <ChevronDown className="w-6 h-6 rotate-90" />
        </button>
        <h1 
          className="text-lg font-medium"
          style={{ color: config?.screenHeaderTitleColor || '#FFFFFF' }}
        >
          {config?.screenHeaderTitle || 'NPS Events 2025'}
        </h1>
        <div className="w-6 h-6" /> {/* Spacer */}
      </div>

      {/* Events List */}
      <div className="px-4 space-y-3 pb-4 overflow-y-auto h-full">
        {eventData.map((dayData) => (
          <div key={dayData.day}>
            {/* Day Header */}
            <div 
              className="rounded-lg p-4 cursor-pointer"
              style={{
                backgroundColor: config?.card?.cardBGColor || '#B68E4A14',
                borderColor: config?.card?.cardBorderColor || '#B68E4A1F',
                border: '1px solid'
              }}
              onClick={() => toggleDay(dayData.day)}
            >
              <div className="flex items-center justify-between">
                <span 
                  className="font-medium"
                  style={{ color: config?.card?.cardTitle?.cardTitleColor || '#F7D955' }}
                >
                  Day {dayData.day}: {dayData.date}
                </span>
                {expandedDays.has(dayData.day) ? (
                  <ChevronUp className="w-5 h-5 text-white" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-white" />
                )}
              </div>
            </div>

            {/* Events */}
            {expandedDays.has(dayData.day) && (
              <div className="mt-2 space-y-2">
                {dayData.events.map((event, index) => (
                  <div 
                    key={index}
                    className="rounded-lg p-4"
                    style={{
                      backgroundColor: config?.card?.cardItem?.cardBGColor || '#000',
                      borderColor: config?.card?.cardItem?.cardBorderColor || '#D3D3D44D',
                      border: '1px solid'
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 
                          className="font-medium text-sm mb-1"
                          style={{ color: config?.card?.cardItem?.cardTitleColor || '#FFF' }}
                        >
                          {event.name}
                        </h3>
                        <div className="flex items-center space-x-2">
                          <div className="flex items-center space-x-1">
                            <div className="w-3 h-3 bg-green-500 rounded-sm flex items-center justify-center">
                              <span className="text-white text-xs">₹</span>
                            </div>
                            <span 
                              className="text-sm font-medium"
                              style={{ color: config?.card?.cardItem?.cardSubTitleColor || '#FFF' }}
                            >
                              BUY-IN: {event.buyIn}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <span 
                          className="text-sm"
                          style={{ color: config?.card?.cardItem?.cardTimerColor || '#7A7C7D' }}
                        >
                          {event.time}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default AllEventsPreview;
