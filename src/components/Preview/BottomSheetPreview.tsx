
import React from 'react';
import LottiePlaceholder from './LottiePlaceholder';

interface BottomSheetPreviewProps {
  config: any;
}

const BottomSheetPreview = ({ config }: BottomSheetPreviewProps) => {
  const { lottie, buttons = [] } = config;

  return (
    <div className="relative h-full bg-gray-800">
      {/* Main Background */}
      <div className="h-2/3 bg-gradient-to-b from-gray-700 to-gray-800 flex items-center justify-center">
        <div className="text-white text-sm opacity-50">Main Screen Content</div>
      </div>
      
      {/* Bottom Sheet */}
      <div className="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl p-6 animate-slide-in-bottom">
        <div className="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4"></div>
        
        {/* Lottie Animation */}
        <div className="flex justify-center mb-6">
          <LottiePlaceholder name={lottie} className="w-24 h-24" />
        </div>
        
        {/* Buttons */}
        <div className="space-y-3">
          {buttons.map((button: any, index: number) => (
            <button
              key={index}
              className="w-full py-3 px-4 rounded-lg font-medium transition-all"
              style={{
                backgroundColor: button.buttonColor,
                color: button.buttonTitleColor,
              }}
            >
              {button.buttonTitle}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BottomSheetPreview;
