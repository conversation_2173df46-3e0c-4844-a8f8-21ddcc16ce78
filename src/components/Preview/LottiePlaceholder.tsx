
import React from 'react';

interface LottiePlaceholderProps {
  name: string;
  className?: string;
}

const LottiePlaceholder = ({ name, className = "w-20 h-20" }: LottiePlaceholderProps) => {
  return (
    <div className={`${className} bg-gradient-to-br from-purple-400 to-pink-400 rounded-lg flex items-center justify-center relative overflow-hidden`}>
      <div className="absolute inset-0 bg-white bg-opacity-20 animate-pulse"></div>
      <div className="text-white text-xs font-medium text-center px-2">
        {name}
      </div>
      <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-ping"></div>
    </div>
  );
};

export default LottiePlaceholder;
