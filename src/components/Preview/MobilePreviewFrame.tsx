
import React from 'react';

interface MobilePreviewFrameProps {
  children: React.ReactNode;
  title?: string;
}

const MobilePreviewFrame = ({ children, title }: MobilePreviewFrameProps) => {
  return (
    <div className="w-80 h-[640px] bg-black rounded-3xl p-2 shadow-2xl">
      {/* Status Bar */}
      <div className="bg-black text-white text-xs flex justify-between items-center px-4 py-1 rounded-t-2xl">
        <span>9:41</span>
        <span className="flex items-center space-x-1">
          <div className="w-4 h-2 border border-white rounded-sm"></div>
          <div className="w-1 h-2 bg-white rounded-sm"></div>
        </span>
      </div>
      
      {/* Screen Content */}
      <div className="bg-gray-900 h-full rounded-b-2xl overflow-hidden relative">
        {title && (
          <div className="absolute top-0 left-0 right-0 z-10 bg-black bg-opacity-50 text-white text-sm px-4 py-2">
            {title}
          </div>
        )}
        <div className="h-full overflow-y-auto">
          {children}
        </div>
      </div>
    </div>
  );
};

export default MobilePreviewFrame;
