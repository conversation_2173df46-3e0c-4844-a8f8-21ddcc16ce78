
import React from 'react';
import { useScrollSpy } from '@/hooks/useScrollSpy';
import MobilePreviewFrame from './MobilePreviewFrame';
import BottomSheetPreview from './BottomSheetPreview';
import WinnerCommonPreview from './WinnerCommonPreview';
import WinnerStatsConfigPreview from './WinnerStatsConfigPreview';
import WinnerConfigPreview from './WinnerConfigPreview';
import WinnerSummaryConfigPreview from './WinnerSummaryConfigPreview';
import WinnerFinalTableConfigPreview from './WinnerFinalTableConfigPreview';
import AllEventsConfigPreview from './AllEventsConfigPreview';

interface PreviewContainerProps {
  jsonData: Record<string, any>;
}

const sectionIds = [
  'winnerPopupModalConfig',
  'winnerCommonConfig',
  'winnerStatsConfig',
  'winnerConfig',
  'winnerSummaryConfig',
  'winnerFinalTableConfig',
  'allEventsConfig'
];

const PreviewContainer = ({ jsonData }: PreviewContainerProps) => {
  const activeSection = useScrollSpy(sectionIds);

  const renderPreview = () => {
    switch (activeSection) {
      case 'winnerPopupModalConfig':
        return (
          <BottomSheetPreview config={jsonData.winnerPopupModalConfig || {}} />
        );
      case 'winnerCommonConfig':
        return (
          <WinnerCommonPreview config={jsonData.winnerCommonConfig || {}} />
        );
      case 'winnerStatsConfig':
        return (
          <WinnerStatsConfigPreview config={jsonData.winnerStatsConfig || {}} />
        );
      case 'winnerConfig':
        return (
          <WinnerConfigPreview config={jsonData.winnerConfig || {}} />
        );
      case 'winnerSummaryConfig':
        return (
          <WinnerSummaryConfigPreview config={jsonData.winnerSummaryConfig || {}} />
        );
      case 'winnerFinalTableConfig':
        return (
          <WinnerFinalTableConfigPreview config={jsonData.winnerFinalTableConfig || {}} />
        );
      case 'allEventsConfig':
        return (
          <AllEventsConfigPreview config={jsonData.allEventsConfig || {}} />
        );
      default:
        return (
          <div className="h-full bg-gray-800 flex items-center justify-center">
            <div className="text-white text-sm">Select a section to preview</div>
          </div>
        );
    }
  };

  return (
    <div className="h-full flex flex-col">
      <MobilePreviewFrame title={`Preview: ${activeSection.replace(/Config$/, '')}`}>
        {renderPreview()}
      </MobilePreviewFrame>
    </div>
  );
};

export default PreviewContainer;
