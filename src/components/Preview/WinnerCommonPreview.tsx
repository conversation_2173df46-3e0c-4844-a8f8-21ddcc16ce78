
import React from 'react';

interface WinnerCommonPreviewProps {
  config: any;
}

const WinnerCommonPreview = ({ config }: WinnerCommonPreviewProps) => {
  // Extract configuration values with fallbacks
  const backgroundImageUri = config?.screenBackgroundImageUri || '';
  const logoConfig = config?.logo || {};
  const logoImageUri = logoConfig?.imageUri || '';
  const logoWidth = logoConfig?.width || 64;
  const logoHeight = logoConfig?.height || 64;
  
  const titleConfig = config?.title || {};
  const titleText = titleConfig?.text || 'WINNER';
  const titleColor = titleConfig?.color || '#E9C372';
  const titleFontSize = titleConfig?.fontSize || 36;
  
  const yearConfig = config?.year || {};
  const yearText = yearConfig?.text || '2024';
  const yearColor = yearConfig?.color || '#FFFFFF';
  const yearFontSize = yearConfig?.fontSize || 18;
  
  const decorativeLineConfig = config?.decorativeLine || {};
  const decorativeLineColor = decorativeLineConfig?.color || '#E9C372';
  const decorativeLineWidth = decorativeLineConfig?.width || 32;
  const decorativeLineHeight = decorativeLineConfig?.height || 1;

  return (
    <div 
      className="h-full relative overflow-hidden"
      style={{
        backgroundImage: backgroundImageUri ? `url(${backgroundImageUri})` : 'none',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundColor: '#1a1a1a'
      }}
    >
      {/* Overlay for better visibility */}
      <div className="absolute inset-0 bg-black bg-opacity-50"></div>
      
      {/* Content */}
      <div className="relative z-10 h-full flex flex-col justify-center items-center">
        {/* Logo */}
        <div className="flex justify-center mb-6">
          {logoImageUri ? (
            <img 
              src={logoImageUri}
              alt="Logo"
              style={{ 
                width: `${logoWidth}px`, 
                height: `${logoHeight}px`,
              }}
              className="object-contain"
            />
          ) : (
            <div 
              className="bg-gradient-to-b from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center"
              style={{ 
                width: `${logoWidth}px`, 
                height: `${logoHeight}px`,
              }}
            >
              <span className="text-black font-bold text-xs">GOAT</span>
            </div>
          )}
        </div>
        
        {/* Title */}
        <div className="mb-4">
          <h1 
            className="font-bold tracking-wider text-center"
            style={{ 
              color: titleColor,
              fontSize: `${titleFontSize}px`,
              lineHeight: '1.1'
            }}
          >
            {titleText}
          </h1>
        </div>
        
        {/* Year with decorative elements */}
        <div className="flex items-center justify-center space-x-3">
          <div 
            style={{ 
              width: `${decorativeLineWidth}px`, 
              height: `${decorativeLineHeight}px`,
              backgroundColor: decorativeLineColor 
            }}
          ></div>
          <span 
            className="font-medium"
            style={{ 
              color: yearColor,
              fontSize: `${yearFontSize}px`
            }}
          >
            {yearText}
          </span>
          <div 
            style={{ 
              width: `${decorativeLineWidth}px`, 
              height: `${decorativeLineHeight}px`,
              backgroundColor: decorativeLineColor 
            }}
          ></div>
        </div>
        
        {/* Common Config Info */}
        <div className="mt-8 text-center">
          <div className="text-white text-sm opacity-75">
            Common Configuration Preview
          </div>
          <div className="text-gray-400 text-xs mt-1">
            Shared settings for all winner screens
          </div>
        </div>
      </div>
    </div>
  );
};

export default WinnerCommonPreview;
