
import React from 'react';

interface WinnerConfigPreviewProps {
  config: any;
}

const WinnerConfigPreview = ({ config }: WinnerConfigPreviewProps) => {
  // Extract winner configuration
  const winnerName = config?.winnerName || '<PERSON>';
  const winnerTitle = config?.winnerTitle || 'GOAT POKER CHAMPIONSHIP';
  const prizeAmount = config?.prizeAmount || '₹10,00,000';
  const winnerImage = config?.winnerImage || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face';
  
  const textStyle = config?.textStyle || {};
  const nameColor = textStyle?.nameColor || '#E9C372';
  const titleColor = textStyle?.titleColor || '#FFFFFF';
  const prizeColor = textStyle?.prizeColor || '#E9C372';

  return (
    <div className="h-full bg-gradient-to-b from-gray-900 to-black relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="w-full h-full bg-repeat" style={{
          backgroundImage: `radial-gradient(circle, #E9C372 1px, transparent 1px)`,
          backgroundSize: '20px 20px'
        }}></div>
      </div>
      
      {/* Content */}
      <div className="relative z-10 h-full flex flex-col justify-center items-center px-6">
        {/* Winner Image */}
        <div className="mb-6">
          <div 
            className="w-32 h-32 rounded-full border-4 overflow-hidden"
            style={{ borderColor: '#E9C372' }}
          >
            <img 
              src={winnerImage}
              alt="Winner"
              className="w-full h-full object-cover"
            />
          </div>
        </div>
        
        {/* Winner Name */}
        <h1 
          className="text-3xl font-bold text-center mb-2"
          style={{ color: nameColor }}
        >
          {winnerName}
        </h1>
        
        {/* Title */}
        <h2 
          className="text-lg text-center mb-4"
          style={{ color: titleColor }}
        >
          {winnerTitle}
        </h2>
        
        {/* Prize Amount */}
        <div 
          className="text-2xl font-bold text-center"
          style={{ color: prizeColor }}
        >
          {prizeAmount}
        </div>
        
        {/* Preview Label */}
        <div className="absolute bottom-4 left-4 right-4 text-center">
          <div className="text-white text-xs opacity-75 bg-black bg-opacity-50 rounded px-2 py-1">
            Winner Config Preview
          </div>
        </div>
      </div>
    </div>
  );
};

export default WinnerConfigPreview;
