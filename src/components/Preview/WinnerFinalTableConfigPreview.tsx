
import React from 'react';

interface WinnerFinalTableConfigPreviewProps {
  config: any;
}

const WinnerFinalTableConfigPreview = ({ config }: WinnerFinalTableConfigPreviewProps) => {
  // Extract final table configuration
  const tableTitle = config?.title || 'FINAL TABLE';
  const players = config?.players || [
    { position: 1, name: '<PERSON>', prize: '₹10,00,000' },
    { position: 2, name: '<PERSON>', prize: '₹6,00,000' },
    { position: 3, name: '<PERSON>', prize: '₹3,50,000' },
    { position: 4, name: '<PERSON>', prize: '₹2,00,000' },
    { position: 5, name: '<PERSON>', prize: '₹1,50,000' }
  ];
  
  const styling = config?.styling || {};
  const titleColor = styling?.titleColor || '#E9C372';
  const winnerColor = styling?.winnerColor || '#FFD700';
  const playerColor = styling?.playerColor || '#FFFFFF';
  const prizeColor = styling?.prizeColor || '#E9C372';

  return (
    <div className="h-full bg-gradient-to-b from-gray-900 to-black relative overflow-hidden">
      {/* Header */}
      <div className="text-center pt-6 pb-4">
        <h1 
          className="text-xl font-bold"
          style={{ color: titleColor }}
        >
          {tableTitle}
        </h1>
      </div>
      
      {/* Players List */}
      <div className="flex-1 px-4 overflow-y-auto">
        <div className="space-y-3">
          {players.map((player: any, index: number) => (
            <div 
              key={index} 
              className={`flex items-center justify-between p-3 rounded-lg ${
                player.position === 1 ? 'bg-yellow-900 bg-opacity-30' : 'bg-gray-800 bg-opacity-50'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div 
                  className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm ${
                    player.position === 1 ? 'bg-yellow-500 text-black' : 'bg-gray-600 text-white'
                  }`}
                >
                  {player.position}
                </div>
                <span 
                  className="font-medium"
                  style={{ 
                    color: player.position === 1 ? winnerColor : playerColor 
                  }}
                >
                  {player.name}
                </span>
              </div>
              <span 
                className="font-semibold text-sm"
                style={{ color: prizeColor }}
              >
                {player.prize}
              </span>
            </div>
          ))}
        </div>
      </div>
      
      {/* Preview Label */}
      <div className="absolute bottom-4 left-4 right-4 text-center">
        <div className="text-white text-xs opacity-75 bg-black bg-opacity-50 rounded px-2 py-1">
          Winner Final Table Config Preview
        </div>
      </div>
    </div>
  );
};

export default WinnerFinalTableConfigPreview;
