
import React from 'react';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';

interface WinnerFinalTablePreviewProps {
  commonConfig: any;
  finalTableConfig: any;
}

const WinnerFinalTablePreview = ({ commonConfig, finalTableConfig }: WinnerFinalTablePreviewProps) => {
  // Mock data for final table players
  const players = [
    { rank: 1, name: 'PokerQueen12345', location: 'Andaman and Nicobar Island', amount: '₹11.92L', avatar: '👑', bgColor: 'from-yellow-600 to-yellow-800', ringColor: 'border-yellow-400' },
    { rank: 2, name: 'Nipunjay12345', location: 'Uttarakhand', amount: '₹2L', avatar: '👤', bgColor: 'from-blue-600 to-blue-800', ringColor: 'border-gray-400' },
    { rank: 3, name: 'RaghavBansal0016', location: 'New Delhi', amount: '₹1.12L', avatar: '🎭', bgColor: 'from-orange-600 to-orange-800', ringColor: 'border-orange-400' },
    { rank: 4, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>ki91', location: 'Haryana', amount: '₹96K', avatar: '👹', bgColor: 'from-gray-600 to-gray-800', ringColor: 'border-gray-500' },
    { rank: 5, name: 'Hemantamaharana1', location: 'Punjab', amount: '₹80K', avatar: '🎭', bgColor: 'from-gray-600 to-gray-800', ringColor: 'border-gray-500' },
    { rank: 6, name: 'Amitkumar1987', location: 'Punjab', amount: '₹50K', avatar: '🐼', bgColor: 'from-gray-600 to-gray-800', ringColor: 'border-gray-500' },
  ];

  return (
    <div 
      className="h-full relative overflow-hidden"
      style={{
        backgroundImage: `url(${commonConfig?.screenBackgroundImageUri})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundColor: '#1a1a1a'
      }}
    >
      {/* Overlay for better visibility */}
      <div className="absolute inset-0 bg-black bg-opacity-60"></div>
      
      {/* Content */}
      <div className="relative z-10 h-full flex flex-col">
        {/* Header Section */}
        <div className="text-center pt-6 pb-4">
          <h1 
            className="text-lg font-medium mb-2"
            style={{ color: finalTableConfig?.screenHeaderTitleColor || '#FFFFFF' }}
          >
            {finalTableConfig?.screenHeaderTitle || 'Final Table'}
          </h1>
          
          {/* Event Title */}
          <div className="flex items-center justify-center mb-2">
            <span className="text-yellow-400 text-sm">✦</span>
            <h2 className="text-yellow-400 font-bold text-base mx-2">
              #1 The MoneyMaker
            </h2>
            <span className="text-yellow-400 text-sm">✦</span>
          </div>
          
          {/* Prize Pool */}
          <div className="text-white text-sm">
            Prize Pool: ₹65.9 L
          </div>
        </div>
        
        {/* Players List */}
        <div className="flex-1 px-4 space-y-2 overflow-y-auto">
          {players.map((player, index) => (
            <div 
              key={index}
              className={`rounded-lg p-3 flex items-center space-x-3 bg-gradient-to-r ${player.bgColor} border border-opacity-30`}
              style={{ 
                backgroundColor: finalTableConfig?.card?.cardBGColor || '#000',
                borderColor: finalTableConfig?.card?.cardBorderColor || '#D3D3D44D'
              }}
            >
              {/* Rank */}
              <div className="flex-shrink-0">
                <div className={`w-6 h-6 rounded border-2 ${player.ringColor} flex items-center justify-center text-xs font-bold`}>
                  {player.rank}
                </div>
              </div>
              
              {/* Avatar */}
              <div className="flex-shrink-0">
                <div className={`w-10 h-10 rounded-full border-2 ${player.ringColor} flex items-center justify-center overflow-hidden`}>
                  <span className="text-lg">{player.avatar}</span>
                </div>
              </div>
              
              {/* Player Info */}
              <div className="flex-1 min-w-0">
                <div 
                  className="text-sm font-medium truncate"
                  style={{ color: finalTableConfig?.card?.fullNameColor || '#FFFFFF' }}
                >
                  {player.name}
                </div>
                <div 
                  className="text-xs truncate"
                  style={{ color: finalTableConfig?.card?.userStateColor || '#909293' }}
                >
                  {player.location}
                </div>
              </div>
              
              {/* Amount and Medal */}
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-green-500 rounded-sm flex items-center justify-center">
                    <span className="text-white text-xs">₹</span>
                  </div>
                  <span 
                    className="text-sm font-bold"
                    style={{ color: finalTableConfig?.card?.amountContainer?.amountColor || '#FFF' }}
                  >
                    {player.amount}
                  </span>
                </div>
                
                {/* Medal for top 3 */}
                {player.rank <= 3 && (
                  <div className="w-6 h-6 flex items-center justify-center">
                    {player.rank === 1 && <span className="text-yellow-400">🥇</span>}
                    {player.rank === 2 && <span className="text-gray-400">🥈</span>}
                    {player.rank === 3 && <span className="text-orange-400">🥉</span>}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WinnerFinalTablePreview;
