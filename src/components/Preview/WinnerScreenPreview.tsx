
import React from 'react';
import { Share2, MapPin } from 'lucide-react';

interface WinnerScreenPreviewProps {
  commonConfig: any;
  winnerConfig: any;
}

const WinnerScreenPreview = ({ commonConfig, winnerConfig }: WinnerScreenPreviewProps) => {
  // Fallback values for missing config
  const backgroundImageUri = commonConfig?.screenBackgroundImageUri || '';
  const bottomButtonTitle = commonConfig?.bottomButton?.buttonTitle || 'View All Events';
  const bottomButtonColor = commonConfig?.bottomButton?.buttonTitleColor || '#D3D3D4';
  
  // Winner config fallbacks
  const cardBGImage = winnerConfig?.cardBGImage || '';
  const shareIconBgColor = winnerConfig?.share?.iconBackgroundColor || 'rgba(0,0,0,0.5)';
  const avatarBorderColor = winnerConfig?.avatar?.borderColor || '#E9C372';
  const userNameColor = winnerConfig?.avatar?.userNameColor || '#E9C372';
  const userIdColor = winnerConfig?.avatar?.userIdColor || '#BDBDBE';
  const userAddressColor = winnerConfig?.avatar?.userAddressColor || '#BDBDBE';
  const winningAmountBGImage = winnerConfig?.winningAmountBGImageUri || '';
  const winningAmountColor = winnerConfig?.winningAmountColor || '#E9C372';

  return (
    <div 
      className="h-full relative overflow-hidden"
      style={{
        backgroundImage: backgroundImageUri ? `url(${backgroundImageUri})` : 'none',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundColor: '#1a1a1a'
      }}
    >
      {/* Overlay for better text visibility */}
      <div className="absolute inset-0 bg-black bg-opacity-40"></div>
      
      {/* Content */}
      <div className="relative z-10 h-full flex flex-col">
        {/* Header Section */}
        <div className="text-center pt-8 pb-4">
          {/* GOAT Logo/Badge */}
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-b from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center">
              <span className="text-black font-bold text-xs">GOAT</span>
            </div>
          </div>
          
          {/* Winner Title */}
          <div className="mb-2">
            <h1 className="text-4xl font-bold text-yellow-400 tracking-wider">
              WINNER
            </h1>
          </div>
          
          {/* Year with decorative elements */}
          <div className="flex items-center justify-center space-x-3">
            <div className="w-8 h-px bg-yellow-400"></div>
            <span className="text-white text-lg font-medium">2024</span>
            <div className="w-8 h-px bg-yellow-400"></div>
          </div>
        </div>
        
        {/* Winner Card */}
        <div className="flex-1 flex items-center justify-center px-6">
          <div 
            className="w-full max-w-sm rounded-2xl p-6 relative"
            style={{
              backgroundImage: cardBGImage ? `url(${cardBGImage})` : 'none',
              backgroundSize: 'cover',
              backgroundColor: 'rgba(0,0,0,0.8)',
              border: '1px solid rgba(255,255,255,0.1)'
            }}
          >
            {/* Share Icon */}
            <div className="absolute top-4 right-4">
              <div 
                className="w-8 h-8 rounded-full flex items-center justify-center"
                style={{ backgroundColor: shareIconBgColor }}
              >
                <Share2 className="w-4 h-4 text-white" />
              </div>
            </div>
            
            {/* Avatar */}
            <div className="flex justify-center mb-4">
              <div 
                className="w-24 h-24 rounded-full border-4 overflow-hidden"
                style={{ borderColor: avatarBorderColor }}
              >
                <img 
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
                  alt="Winner"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
            
            {/* User Details */}
            <div className="text-center mb-4">
              <h3 
                className="text-lg font-semibold mb-1"
                style={{ color: userNameColor }}
              >
                Ashish Kumar
              </h3>
              <p 
                className="text-sm mb-2"
                style={{ color: userIdColor }}
              >
                Ashishk1980
              </p>
              
              {/* Location */}
              <div className="flex items-center justify-center space-x-1">
                <MapPin 
                  className="w-3 h-3"
                  style={{ color: userAddressColor }}
                />
                <span 
                  className="text-xs"
                  style={{ color: userAddressColor }}
                >
                  Panchkula, Chandigarh
                </span>
              </div>
            </div>
            
            {/* Divider */}
            <div className="flex justify-center mb-4">
              <div className="w-8 h-px bg-yellow-400"></div>
            </div>
            
            {/* Winning Amount */}
            <div 
              className="text-center py-3 px-6 rounded-lg mb-6 mx-4"
              style={{
                backgroundImage: winningAmountBGImage ? `url(${winningAmountBGImage})` : 'none',
                backgroundSize: 'cover',
                backgroundColor: 'rgba(0,0,0,0.6)',
                border: '1px solid rgba(255,215,0,0.3)'
              }}
            >
              <span 
                className="text-xl font-bold"
                style={{ color: winningAmountColor }}
              >
                WON - ₹1Cr.
              </span>
            </div>
            
            {/* Action Button */}
            <button
              className="w-full py-3 px-4 rounded-lg text-base font-semibold"
              style={{
                backgroundColor: '#E9C372',
                color: '#212427'
              }}
            >
              View Profile
            </button>
          </div>
        </div>
        
        {/* Bottom Section */}
        <div className="pb-6">
          {/* Pagination */}
          <div className="flex justify-center space-x-2 mb-4">
            <div className="w-6 h-1 bg-yellow-400 rounded"></div>
            {[...Array(8)].map((_, i) => (
              <div key={i} className="w-1 h-1 bg-gray-500 rounded-full"></div>
            ))}
          </div>
          
          {/* Bottom Button */}
          <div className="text-center">
            <button 
              className="text-sm px-4 py-2 rounded flex items-center justify-center mx-auto space-x-2"
              style={{ color: bottomButtonColor }}
            >
              <span>{bottomButtonTitle}</span>
              <div className="w-3 h-3 border-r border-t border-current transform rotate-45"></div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WinnerScreenPreview;
