
import React from 'react';

interface WinnerStatsConfigPreviewProps {
  config: any;
}

const WinnerStatsConfigPreview = ({ config }: WinnerStatsConfigPreviewProps) => {
  // Extract stats configuration
  const stats = config?.stats || [
    { label: 'Crorepatis', value: '64', color: '#E9C372' },
    { label: 'Lakhpatis', value: '89', color: '#E9C372' },
    { label: 'ITM Players', value: '1098', color: '#E9C372' },
    { label: 'Made money from Satty', value: '24', color: '#E9C372' }
  ];
  
  const cardStyle = config?.cardStyle || {};
  const cardBackgroundColor = cardStyle?.backgroundColor || 'rgba(0,0,0,0.6)';
  const cardBorderColor = cardStyle?.borderColor || 'rgba(233,195,114,0.3)';
  const cardBorderRadius = cardStyle?.borderRadius || 16;

  return (
    <div className="h-full bg-gradient-to-b from-gray-900 to-black relative overflow-hidden">
      {/* Header */}
      <div className="text-center pt-8 pb-6">
        <h1 className="text-2xl font-bold text-yellow-400 mb-2">STATS</h1>
        <div className="text-white text-sm">2024</div>
      </div>
      
      {/* Stats Grid */}
      <div className="flex-1 px-6">
        <div className="grid grid-cols-2 gap-4 max-w-sm mx-auto">
          {stats.map((stat: any, index: number) => (
            <div 
              key={index}
              className="rounded-2xl p-4 border"
              style={{
                backgroundColor: cardBackgroundColor,
                borderColor: cardBorderColor,
                borderRadius: `${cardBorderRadius}px`,
                borderWidth: '1px'
              }}
            >
              <div className="text-center">
                <div 
                  className="text-3xl font-bold mb-1"
                  style={{ color: stat.color || '#E9C372' }}
                >
                  {stat.value}
                </div>
                <div 
                  className="text-sm"
                  style={{ color: stat.labelColor || '#E9C372' }}
                >
                  {stat.label}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Preview Label */}
      <div className="absolute bottom-4 left-4 right-4 text-center">
        <div className="text-white text-xs opacity-75 bg-black bg-opacity-50 rounded px-2 py-1">
          Winner Stats Config Preview
        </div>
      </div>
    </div>
  );
};

export default WinnerStatsConfigPreview;
