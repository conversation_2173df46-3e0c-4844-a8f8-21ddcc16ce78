
import React from 'react';

interface WinnerStatsPreviewProps {
  commonConfig: any;
  statsConfig: any;
}

const WinnerStatsPreview = ({ commonConfig, statsConfig }: WinnerStatsPreviewProps) => {
  // Extract configuration values with fallbacks
  const backgroundImageUri = commonConfig?.screenBackgroundImageUri || '';
  const logoConfig = commonConfig?.logo || {};
  const logoImageUri = logoConfig?.imageUri || '';
  const logoWidth = logoConfig?.width || 64;
  const logoHeight = logoConfig?.height || 64;
  
  const titleConfig = commonConfig?.title || {};
  const titleText = titleConfig?.text || 'SUMMARY';
  const titleColor = titleConfig?.color || '#E9C372';
  const titleFontSize = titleConfig?.fontSize || 36;
  
  const yearConfig = commonConfig?.year || {};
  const yearText = yearConfig?.text || '2024';
  const yearColor = yearConfig?.color || '#FFFFFF';
  const yearFontSize = yearConfig?.fontSize || 18;
  
  const decorativeLineConfig = commonConfig?.decorativeLine || {};
  const decorativeLineColor = decorativeLineConfig?.color || '#E9C372';
  const decorativeLineWidth = decorativeLineConfig?.width || 32;
  const decorativeLineHeight = decorativeLineConfig?.height || 1;
  
  // Avatar section configuration
  const avatarSectionConfig = commonConfig?.avatarSection || {};
  const mainAvatarConfig = avatarSectionConfig?.mainAvatar || {};
  const mainAvatarImageUri = mainAvatarConfig?.imageUri || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face';
  const mainAvatarBorderColor = mainAvatarConfig?.borderColor || '#E9C372';
  const mainAvatarSize = mainAvatarConfig?.size || 96;
  
  const leftAvatarConfig = avatarSectionConfig?.leftAvatar || {};
  const leftAvatarImageUri = leftAvatarConfig?.imageUri || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face';
  const leftAvatarBorderColor = leftAvatarConfig?.borderColor || '#E9C372';
  const leftAvatarSize = leftAvatarConfig?.size || 64;
  
  const rightAvatarConfig = avatarSectionConfig?.rightAvatar || {};
  const rightAvatarImageUri = rightAvatarConfig?.imageUri || 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face';
  const rightAvatarBorderColor = rightAvatarConfig?.borderColor || '#E9C372';
  const rightAvatarSize = rightAvatarConfig?.size || 64;
  
  // Stats cards configuration
  const statsCards = statsConfig?.stats || [
    { label: 'Crorepatis', value: '64', color: '#E9C372' },
    { label: 'Lakhpatis', value: '89', color: '#E9C372' },
    { label: 'ITM Players', value: '1098', color: '#E9C372' },
    { label: 'Made money from Satty', value: '24', color: '#E9C372' }
  ];
  
  const statsCardConfig = statsConfig?.cardStyle || {};
  const cardBackgroundColor = statsCardConfig?.backgroundColor || 'rgba(0,0,0,0.6)';
  const cardBorderColor = statsCardConfig?.borderColor || 'rgba(233,195,114,0.3)';
  const cardBorderRadius = statsCardConfig?.borderRadius || 16;
  
  // Pagination configuration
  const paginationConfig = commonConfig?.pagination || {};
  const currentPage = paginationConfig?.currentPage || 1;
  const totalPages = paginationConfig?.totalPages || 9;
  const activeDotColor = paginationConfig?.activeDotColor || '#E9C372';
  const inactiveDotColor = paginationConfig?.inactiveDotColor || '#6B7280';
  
  // Bottom button configuration
  const bottomButtonConfig = commonConfig?.bottomButton || {};
  const bottomButtonTitle = bottomButtonConfig?.buttonTitle || 'View All Events';
  const bottomButtonColor = bottomButtonConfig?.buttonTitleColor || '#D3D3D4';
  const bottomButtonIconColor = bottomButtonConfig?.iconColor || '#D3D3D4';

  return (
    <div 
      className="h-full relative overflow-hidden"
      style={{
        backgroundImage: backgroundImageUri ? `url(${backgroundImageUri})` : 'none',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundColor: '#1a1a1a'
      }}
    >
      {/* Overlay for better visibility */}
      <div className="absolute inset-0 bg-black bg-opacity-50"></div>
      
      {/* Content */}
      <div className="relative z-10 h-full flex flex-col">
        {/* Header Section */}
        <div className="text-center pt-8 pb-6">
          {/* Logo */}
          <div className="flex justify-center mb-4">
            {logoImageUri ? (
              <img 
                src={logoImageUri}
                alt="Logo"
                style={{ 
                  width: `${logoWidth}px`, 
                  height: `${logoHeight}px`,
                }}
                className="object-contain"
              />
            ) : (
              <div 
                className="bg-gradient-to-b from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center"
                style={{ 
                  width: `${logoWidth}px`, 
                  height: `${logoHeight}px`,
                }}
              >
                <span className="text-black font-bold text-xs">GOAT</span>
              </div>
            )}
          </div>
          
          {/* Title */}
          <div className="mb-2">
            <h1 
              className="font-bold tracking-wider"
              style={{ 
                color: titleColor,
                fontSize: `${titleFontSize}px`,
                lineHeight: '1.1'
              }}
            >
              {titleText}
            </h1>
          </div>
          
          {/* Year with decorative elements */}
          <div className="flex items-center justify-center space-x-3">
            <div 
              style={{ 
                width: `${decorativeLineWidth}px`, 
                height: `${decorativeLineHeight}px`,
                backgroundColor: decorativeLineColor 
              }}
            ></div>
            <span 
              className="font-medium"
              style={{ 
                color: yearColor,
                fontSize: `${yearFontSize}px`
              }}
            >
              {yearText}
            </span>
            <div 
              style={{ 
                width: `${decorativeLineWidth}px`, 
                height: `${decorativeLineHeight}px`,
                backgroundColor: decorativeLineColor 
              }}
            ></div>
          </div>
        </div>
        
        {/* Character Avatars Section */}
        <div className="flex justify-center mb-8">
          <div className="relative">
            {/* Main center avatar */}
            <div 
              className="rounded-full border-4 overflow-hidden relative z-30"
              style={{ 
                width: `${mainAvatarSize}px`, 
                height: `${mainAvatarSize}px`,
                borderColor: mainAvatarBorderColor 
              }}
            >
              <img 
                src={mainAvatarImageUri}
                alt="Main character"
                className="w-full h-full object-cover"
              />
            </div>
            
            {/* Left avatar */}
            <div 
              className="absolute top-2 rounded-full border-3 overflow-hidden z-20"
              style={{ 
                left: `-${leftAvatarSize / 2}px`,
                width: `${leftAvatarSize}px`, 
                height: `${leftAvatarSize}px`,
                borderColor: leftAvatarBorderColor,
                borderWidth: '3px'
              }}
            >
              <img 
                src={leftAvatarImageUri}
                alt="Left character"
                className="w-full h-full object-cover"
              />
            </div>
            
            {/* Right avatar */}
            <div 
              className="absolute top-2 rounded-full border-3 overflow-hidden z-20"
              style={{ 
                right: `-${rightAvatarSize / 2}px`,
                width: `${rightAvatarSize}px`, 
                height: `${rightAvatarSize}px`,
                borderColor: rightAvatarBorderColor,
                borderWidth: '3px'
              }}
            >
              <img 
                src={rightAvatarImageUri}
                alt="Right character"
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
        
        {/* Stats Cards Grid */}
        <div className="flex-1 px-6">
          <div className="grid grid-cols-2 gap-4 max-w-sm mx-auto">
            {statsCards.map((stat: any, index: number) => (
              <div 
                key={index}
                className="rounded-2xl p-4 border"
                style={{
                  backgroundColor: cardBackgroundColor,
                  borderColor: cardBorderColor,
                  borderRadius: `${cardBorderRadius}px`,
                  borderWidth: '1px'
                }}
              >
                <div className="text-center">
                  <div 
                    className="text-3xl font-bold mb-1"
                    style={{ color: stat.color || '#E9C372' }}
                  >
                    {stat.value}
                  </div>
                  <div 
                    className="text-sm"
                    style={{ color: stat.labelColor || '#E9C372' }}
                  >
                    {stat.label}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Bottom Section */}
        <div className="pb-6">
          {/* Pagination */}
          <div className="flex justify-center items-center space-x-2 mb-4">
            {Array.from({ length: totalPages }, (_, i) => (
              <div 
                key={i} 
                className={`rounded-full ${i === currentPage - 1 ? 'w-6 h-1' : 'w-1 h-1'}`}
                style={{ 
                  backgroundColor: i === currentPage - 1 ? activeDotColor : inactiveDotColor 
                }}
              ></div>
            ))}
            <div 
              className="text-xs ml-2"
              style={{ color: activeDotColor }}
            >
              {currentPage}/{totalPages}
            </div>
          </div>
          
          {/* Bottom Button */}
          <div className="text-center">
            <button 
              className="text-sm px-4 py-2 rounded flex items-center justify-center mx-auto space-x-2"
              style={{ color: bottomButtonColor }}
            >
              <span>{bottomButtonTitle}</span>
              <div 
                className="w-3 h-3 border-r border-t transform rotate-45"
                style={{ borderColor: bottomButtonIconColor }}
              ></div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WinnerStatsPreview;
