
import React from 'react';

interface WinnerSummaryConfigPreviewProps {
  config: any;
}

const WinnerSummaryConfigPreview = ({ config }: WinnerSummaryConfigPreviewProps) => {
  // Extract summary configuration
  const summaryTitle = config?.title || 'TOURNAMENT SUMMARY';
  const summaryData = config?.summaryData || [
    { label: 'Total Players', value: '2,500' },
    { label: 'Prize Pool', value: '₹50,00,000' },
    { label: 'Duration', value: '8 hours' },
    { label: 'Buy-in', value: '₹2,000' }
  ];
  
  const styling = config?.styling || {};
  const titleColor = styling?.titleColor || '#E9C372';
  const valueColor = styling?.valueColor || '#FFFFFF';
  const labelColor = styling?.labelColor || '#9CA3AF';

  return (
    <div className="h-full bg-gradient-to-b from-gray-900 to-black relative overflow-hidden">
      {/* Header */}
      <div className="text-center pt-8 pb-8">
        <h1 
          className="text-2xl font-bold"
          style={{ color: titleColor }}
        >
          {summaryTitle}
        </h1>
      </div>
      
      {/* Summary Grid */}
      <div className="flex-1 px-6">
        <div className="space-y-6 max-w-sm mx-auto">
          {summaryData.map((item: any, index: number) => (
            <div key={index} className="flex justify-between items-center py-3 border-b border-gray-700">
              <span 
                className="text-sm"
                style={{ color: labelColor }}
              >
                {item.label}
              </span>
              <span 
                className="text-lg font-semibold"
                style={{ color: valueColor }}
              >
                {item.value}
              </span>
            </div>
          ))}
        </div>
      </div>
      
      {/* Preview Label */}
      <div className="absolute bottom-4 left-4 right-4 text-center">
        <div className="text-white text-xs opacity-75 bg-black bg-opacity-50 rounded px-2 py-1">
          Winner Summary Config Preview
        </div>
      </div>
    </div>
  );
};

export default WinnerSummaryConfigPreview;
