
import React from 'react';

interface WinnerSummaryPreviewProps {
  commonConfig: any;
  summaryConfig: any;
}

const WinnerSummaryPreview = ({ commonConfig, summaryConfig }: WinnerSummaryPreviewProps) => {
  return (
    <div 
      className="h-full relative overflow-hidden"
      style={{
        backgroundImage: `url(${commonConfig.screenBackgroundImageUri})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundColor: '#1a1a1a'
      }}
    >
      {/* Overlay for better visibility */}
      <div className="absolute inset-0 bg-black bg-opacity-50"></div>
      
      {/* Content */}
      <div className="relative z-10 h-full flex flex-col">
        {/* Header Section */}
        <div className="text-center pt-8 pb-6">
          {/* GOAT Logo/Badge */}
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-b from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center">
              <span className="text-black font-bold text-xs">GOAT</span>
            </div>
          </div>
          
          {/* Summary Title */}
          <div className="mb-2">
            <h1 className="text-4xl font-bold text-yellow-400 tracking-wider">
              SUMMARY
            </h1>
          </div>
          
          {/* Year with decorative elements */}
          <div className="flex items-center justify-center space-x-3">
            <div className="w-8 h-px bg-yellow-400"></div>
            <span 
              className="text-lg font-medium"
              style={{ color: summaryConfig.headerSubtitleColor || '#FFFFFF' }}
            >
              {summaryConfig.headerSubtitle || '2024'}
            </span>
            <div className="w-8 h-px bg-yellow-400"></div>
          </div>
        </div>
        
        {/* Main Content Card */}
        <div className="flex-1 px-6 flex items-center justify-center">
          <div className="bg-black bg-opacity-60 rounded-2xl p-6 border border-yellow-400 border-opacity-30 w-full max-w-sm">
            <div className="text-center">
              {/* Tournament Name */}
              <div className="text-xl font-bold text-yellow-400 mb-4">
                {summaryConfig.tournamentName || 'ALL EVENTS 2025'}
              </div>
              
              {/* Placeholder content for summary */}
              <div className="space-y-4">
                <div className="text-white text-sm">
                  Event Summary Content
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="bg-gray-800 rounded p-2">
                    <div className="text-yellow-400 font-bold">156</div>
                    <div className="text-gray-300">Events</div>
                  </div>
                  <div className="bg-gray-800 rounded p-2">
                    <div className="text-yellow-400 font-bold">₹2.5Cr</div>
                    <div className="text-gray-300">Total Prize</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Bottom Section */}
        <div className="pb-6">
          {/* Pagination */}
          <div className="flex justify-center space-x-2 mb-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} className={`w-1 h-1 rounded-full ${i === 7 ? 'bg-yellow-400' : 'bg-gray-500'}`}></div>
            ))}
            <div className="text-yellow-400 text-xs ml-2">8/9</div>
          </div>
          
          {/* Bottom Button */}
          <div className="text-center">
            <button 
              className="text-sm px-4 py-2 rounded flex items-center justify-center mx-auto space-x-2"
              style={{ color: commonConfig.bottomButton?.buttonTitleColor || '#D3D3D4' }}
            >
              <span>{commonConfig.bottomButton?.buttonTitle || 'View All Events'}</span>
              <div className="w-3 h-3 border-r border-t border-current transform rotate-45"></div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WinnerSummaryPreview;
