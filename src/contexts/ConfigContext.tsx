import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { FileStorageService } from '@/services/fileStorageService';
import { S3Config } from '@/services/s3Service';
import { toast } from '@/hooks/use-toast';
import { winnersScreenDefaultConfig } from '@/data/defaultConfigs';

export interface ConfigField {
  id: string;
  key: string;
  label: string;
  type: 'text' | 'color' | 'toggle' | 'image' | 'select' | 'number' | 'textarea' | 'url';
  value: any;
  required?: boolean;
  tooltip?: string;
  options?: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export interface ConfigBlock {
  id: string;
  name: string;
  description: string;
  fields: ConfigField[];
  nested?: ConfigBlock[];
}

export interface Configuration {
  id: string;
  name: string;
  environment: 'dev' | 'staging' | 'prod';
  blocks: ConfigBlock[];
  lastModified: string;
  version: number;
  jsonData: Record<string, any>;
}

interface ConfigContextType {
  configurations: Configuration[];
  currentEnvironment: 'dev' | 'staging' | 'prod';
  selectedConfig: Configuration | null;
  isLoading: boolean;
  isS3Connected: boolean;
  s3Config: S3Config | null;
  setCurrentEnvironment: (env: 'dev' | 'staging' | 'prod') => void;
  setSelectedConfig: (config: Configuration | null) => void;
  addConfiguration: (config: Omit<Configuration, 'id' | 'lastModified' | 'version'>) => Promise<boolean>;
  updateConfiguration: (id: string, updates: Partial<Configuration>) => Promise<boolean>;
  deleteConfiguration: (id: string) => Promise<void>;
  duplicateConfiguration: (config: Configuration) => Promise<void>;
  generateUIFromJSON: (jsonData: Record<string, any>) => ConfigBlock[];
  loadConfigurations: () => Promise<void>;
  connectS3: (s3Config: S3Config) => void;
}

const ConfigContext = createContext<ConfigContextType | undefined>(undefined);

export const useConfig = () => {
  const context = useContext(ConfigContext);
  if (!context) {
    throw new Error('useConfig must be used within a ConfigProvider');
  }
  return context;
};

const generateUIFromJSON = (jsonData: Record<string, any>): ConfigBlock[] => {
  const blocks: ConfigBlock[] = [];

  const isImageField = (key: string, value: any): boolean => {
    const imageKeywords = ['image', 'img', 'photo', 'picture', 'avatar', 'banner', 'icon', 'logo'];
    const lowerKey = key.toLowerCase();
    
    // Check if key contains image-related keywords
    const hasImageKeyword = imageKeywords.some(keyword => lowerKey.includes(keyword));
    
    // Check if value looks like an image URL
    const isImageUrl = typeof value === 'string' && (
      !!value.match(/\.(jpg|jpeg|png|gif|webp|svg)(\?.*)?$/i) ||
      value.includes('image') ||
      value.includes('photo')
    );
    
    return hasImageKeyword || isImageUrl;
  };

  const createFieldsFromObject = (obj: any, parentKey: string = '', level: number = 0): ConfigField[] => {
    const fields: ConfigField[] = [];

    Object.keys(obj).forEach(fieldKey => {
      const value = obj[fieldKey];
      const fullKey = parentKey ? `${parentKey}.${fieldKey}` : fieldKey;

      if (Array.isArray(value)) {
        // Handle arrays by creating fields for each item's properties
        value.forEach((item, index) => {
          if (typeof item === 'object' && item !== null) {
            Object.keys(item).forEach(itemKey => {
              const itemValue = item[itemKey];
              const arrayFieldKey = `${fullKey}[${index}].${itemKey}`;
              let fieldType: ConfigField['type'] = 'text';

              // Auto-detect field type with improved image detection
              if (typeof itemValue === 'boolean') {
                fieldType = 'toggle';
              } else if (typeof itemValue === 'number') {
                fieldType = 'number';
              } else if (itemKey.toLowerCase().includes('color') || (typeof itemValue === 'string' && itemValue.startsWith('#'))) {
                fieldType = 'color';
              } else if (isImageField(itemKey, itemValue)) {
                fieldType = 'image';
              } else if (itemKey.toLowerCase().includes('url') || itemKey.toLowerCase().includes('uri') || itemKey.toLowerCase().includes('link')) {
                fieldType = 'url';
              }

              fields.push({
                id: `${parentKey}-${arrayFieldKey}`,
                key: arrayFieldKey,
                label: `${fieldKey} ${index + 1} - ${itemKey.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}`,
                type: fieldType,
                value: itemValue,
                required: false,
                tooltip: `Configure ${itemKey} for ${fieldKey} item ${index + 1}`
              });
            });
          } else {
            // Handle primitive array items
            fields.push({
              id: `${parentKey}-${fullKey}[${index}]`,
              key: `${fullKey}[${index}]`,
              label: `${fieldKey} ${index + 1}`,
              type: typeof item === 'number' ? 'number' : 'text',
              value: item,
              required: false,
              tooltip: `Configure ${fieldKey} item ${index + 1}`
            });
          }
        });
      } else if (typeof value === 'object' && value !== null) {
        // Handle nested objects by creating fields for each property
        Object.keys(value).forEach(nestedKey => {
          const nestedValue = value[nestedKey];
          const nestedFieldKey = `${fullKey}.${nestedKey}`;
          let fieldType: ConfigField['type'] = 'text';

          // Auto-detect field type with improved image detection
          if (typeof nestedValue === 'boolean') {
            fieldType = 'toggle';
          } else if (typeof nestedValue === 'number') {
            fieldType = 'number';
          } else if (nestedKey.toLowerCase().includes('color') || (typeof nestedValue === 'string' && nestedValue.startsWith('#'))) {
            fieldType = 'color';
          } else if (isImageField(nestedKey, nestedValue)) {
            fieldType = 'image';
          } else if (nestedKey.toLowerCase().includes('url') || nestedKey.toLowerCase().includes('uri') || nestedKey.toLowerCase().includes('link')) {
            fieldType = 'url';
          } else if (typeof nestedValue === 'object' && nestedValue !== null) {
            fieldType = 'textarea';
          }

          fields.push({
            id: `${parentKey}-${nestedFieldKey}`,
            key: nestedFieldKey,
            label: `${fieldKey} - ${nestedKey.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}`,
            type: fieldType,
            value: typeof nestedValue === 'object' ? JSON.stringify(nestedValue, null, 2) : nestedValue,
            required: false,
            tooltip: `Configure ${nestedKey} for ${fieldKey}`
          });
        });
      } else {
        // Handle primitive values with improved image detection
        let fieldType: ConfigField['type'] = 'text';

        if (typeof value === 'boolean') {
          fieldType = 'toggle';
        } else if (typeof value === 'number') {
          fieldType = 'number';
        } else if (fieldKey.toLowerCase().includes('color') || (typeof value === 'string' && value.startsWith('#'))) {
          fieldType = 'color';
        } else if (isImageField(fieldKey, value)) {
          fieldType = 'image';
        } else if (fieldKey.toLowerCase().includes('url') || fieldKey.toLowerCase().includes('uri') || fieldKey.toLowerCase().includes('link')) {
          fieldType = 'url';
        }

        fields.push({
          id: `${parentKey}-${fullKey}`,
          key: fullKey,
          label: fieldKey.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
          type: fieldType,
          value: value,
          required: false,
          tooltip: `Configure ${fieldKey}`
        });
      }
    });

    return fields;
  };

  Object.keys(jsonData).forEach(blockKey => {
    const blockData = jsonData[blockKey];
    const fields = createFieldsFromObject(blockData, blockKey);

    blocks.push({
      id: blockKey,
      name: blockKey.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
      description: `Configuration for ${blockKey}`,
      fields
    });
  });

  return blocks;
};

export const ConfigProvider = ({ children }: { children: ReactNode }) => {
  const [configurations, setConfigurations] = useState<Configuration[]>([]);
  const [currentEnvironment, setCurrentEnvironment] = useState<'dev' | 'staging' | 'prod'>('dev');
  const [selectedConfig, setSelectedConfig] = useState<Configuration | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  // Initialize with IAM role credentials automatically
  const [fileStorageService, setFileStorageService] = useState<FileStorageService | null>(() => {
    try {
      // Fetch environment variables at runtime
      const bucketName = import.meta.env.VITE_S3_BUCKET_NAME;
      const region = import.meta.env.VITE_AWS_DEFAULT_REGION || 'us-east-1';
      
      console.log('Environment variables:', { bucketName, region });
      
      // Only initialize if bucket name is available
      if (!bucketName) {
        console.warn('VITE_S3_BUCKET_NAME not configured, S3 storage disabled');
        return null;
      }

      const iamS3Config: S3Config = {
        region,
        bucketName,
        // No accessKeyId or secretAccessKey needed - IAM role provides these
      };
      
      console.log('Initializing FileStorageService with config:', iamS3Config);
      return new FileStorageService(iamS3Config);
    } catch (error) {
      console.error('Failed to initialize FileStorageService:', error);
      return null;
    }
  });
  const [isS3Connected, setIsS3Connected] = useState(true); // Always true with IAM role
  const [s3Config, setS3Config] = useState<S3Config | null>(null);

  // Load configurations on mount and environment change
  useEffect(() => {
    loadConfigurations();
  }, [currentEnvironment, fileStorageService]);

  // Auto-create Winners Screen Config if it doesn't exist
  useEffect(() => {
    const checkAndCreateDefaultConfig = async () => {
      const existingConfigs = await fileStorageService.loadConfigurationsByEnvironment(currentEnvironment);
      const winnersConfigExists = existingConfigs.some(config => 
        config.name === 'Winners Screen Config'
      );
      
      if (!winnersConfigExists) {
        await addConfiguration({
          name: 'Winners Screen Config',
          environment: currentEnvironment,
          blocks: [],
          jsonData: winnersScreenDefaultConfig
        });
      }
    };
    
    checkAndCreateDefaultConfig();
  }, [currentEnvironment, fileStorageService]);

  const connectS3 = (newS3Config: S3Config) => {
    try {
      const newFileStorageService = new FileStorageService(newS3Config);
      setFileStorageService(newFileStorageService);
      setS3Config(newS3Config);
      setIsS3Connected(newFileStorageService.isS3Available());
      
      if (newFileStorageService.isS3Available()) {
        toast({
          title: "S3 Connected",
          description: "Successfully connected to AWS S3. Configurations will now be saved to S3."
        });
      } else {
        toast({
          title: "S3 Connection Failed",
          description: "Failed to connect to S3. Using localStorage as fallback.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error connecting to S3:', error);
      toast({
        title: "S3 Connection Error",
        description: "An error occurred while connecting to S3.",
        variant: "destructive"
      });
    }
  };

  const loadConfigurations = async () => {
    if (!fileStorageService) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const storedConfigs = await fileStorageService.loadConfigurationsByEnvironment(currentEnvironment);
      
      // Convert stored configurations to full Configuration objects
      const fullConfigs: Configuration[] = storedConfigs.map(stored => ({
        id: stored.id,
        name: stored.name,
        environment: stored.environment,
        blocks: generateUIFromJSON(stored.jsonData),
        lastModified: stored.lastModified,
        version: stored.version,
        jsonData: stored.jsonData
      }));
      
      setConfigurations(fullConfigs);
    } catch (error) {
      console.error('Failed to load configurations:', error);
      toast({
        title: "Load Error",
        description: "Failed to load configurations.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const addConfiguration = async (config: Omit<Configuration, 'id' | 'lastModified' | 'version'>): Promise<boolean> => {
    setIsLoading(true);
    try {
      const newId = `config-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const timestamp = new Date().toISOString();
      
      const storedConfig = {
        id: newId,
        name: config.name,
        environment: config.environment,
        jsonData: config.jsonData,
        lastModified: timestamp,
        version: 1
      };

      const success = await fileStorageService.saveConfiguration(storedConfig);
      
      if (success) {
        const newConfig: Configuration = {
          ...storedConfig,
          blocks: generateUIFromJSON(config.jsonData)
        };

        setConfigurations(prev => [...prev, newConfig]);
        
        toast({
          title: "Configuration saved",
          description: `${config.name} has been created and saved${isS3Connected ? ' to S3' : ''}.`
        });
        
        return true;
      } else {
        toast({
          title: "Save Error",
          description: "Failed to save configuration.",
          variant: "destructive"
        });
        return false;
      }
    } catch (error) {
      console.error('Error adding configuration:', error);
      toast({
        title: "Save Error",
        description: "An unexpected error occurred while saving.",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const updateConfiguration = async (id: string, updates: Partial<Configuration>): Promise<boolean> => {
    setIsLoading(true);
    try {
      const existingConfig = configurations.find(c => c.id === id);
      if (!existingConfig) {
        toast({
          title: "Update Error",
          description: "Configuration not found.",
          variant: "destructive"
        });
        return false;
      }

      const updateData = {
        jsonData: updates.jsonData || existingConfig.jsonData,
        version: existingConfig.version + 1
      };

      const success = await fileStorageService.updateConfiguration(id, updateData);

      if (success) {
        const updatedConfig = {
          ...existingConfig,
          ...updates,
          lastModified: new Date().toISOString(),
          version: updateData.version,
          blocks: updates.jsonData ? generateUIFromJSON(updates.jsonData) : existingConfig.blocks
        };

        setConfigurations(prev => prev.map(config => 
          config.id === id ? updatedConfig : config
        ));
        
        if (selectedConfig?.id === id) {
          setSelectedConfig(updatedConfig);
        }

        toast({
          title: "Configuration updated",
          description: `Changes saved as version ${updateData.version}${isS3Connected ? ' to S3' : ''}.`
        });
        
        return true;
      } else {
        toast({
          title: "Update Error",
          description: "Failed to update configuration.",
          variant: "destructive"
        });
        return false;
      }
    } catch (error) {
      console.error('Error updating configuration:', error);
      toast({
        title: "Update Error",
        description: "An unexpected error occurred while updating.",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteConfiguration = async (id: string) => {
    setIsLoading(true);
    try {
      const success = await fileStorageService.deleteConfiguration(id);
      
      if (success) {
        setConfigurations(prev => prev.filter(config => config.id !== id));
        
        if (selectedConfig?.id === id) {
          setSelectedConfig(null);
        }
        
        toast({
          title: "Configuration deleted",
          description: `Configuration has been removed${isS3Connected ? ' from S3' : ''}.`
        });
      } else {
        toast({
          title: "Delete Error",
          description: "Failed to delete configuration.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error deleting configuration:', error);
      toast({
        title: "Delete Error",
        description: "An unexpected error occurred while deleting.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const duplicateConfiguration = async (config: Configuration): Promise<void> => {
    setIsLoading(true);
    try {
      const newId = `config-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const timestamp = new Date().toISOString();
      
      const duplicatedConfig = {
        id: newId,
        name: `${config.name} (Copy)`,
        environment: config.environment,
        jsonData: { ...config.jsonData },
        lastModified: timestamp,
        version: 1
      };

      const success = await fileStorageService.saveConfiguration(duplicatedConfig);
      
      if (success) {
        const newConfig: Configuration = {
          ...duplicatedConfig,
          blocks: generateUIFromJSON(duplicatedConfig.jsonData)
        };

        setConfigurations(prev => [...prev, newConfig]);
        
        toast({
          title: "Configuration duplicated",
          description: `${config.name} has been duplicated successfully${isS3Connected ? ' to S3' : ''}.`
        });
      } else {
        throw new Error('Failed to save duplicated configuration');
      }
    } catch (error) {
      console.error('Error duplicating configuration:', error);
      toast({
        title: "Duplicate Error",
        description: "An unexpected error occurred while duplicating.",
        variant: "destructive"
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ConfigContext.Provider value={{
      configurations,
      currentEnvironment,
      selectedConfig,
      isLoading,
      isS3Connected,
      s3Config,
      setCurrentEnvironment,
      setSelectedConfig,
      addConfiguration,
      updateConfiguration,
      deleteConfiguration,
      duplicateConfiguration,
      generateUIFromJSON,
      loadConfigurations,
      connectS3
    }}>
      {children}
    </ConfigContext.Provider>
  );
};
