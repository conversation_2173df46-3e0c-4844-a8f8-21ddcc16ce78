
export const winnersScreenDefaultConfig = {
  "winnerPopupModalConfig": {
    "lottie": "NPSBottomSheet",
    "buttons": [
      {
        "buttonTitle": "Checkout Winners",
        "buttonTitleColor": "#212427",
        "buttonColor": "#E9C372",
        "redirectionDetails": {
          "redirectionType": "SCREEN",
          "redirectionParams": {
            "redirectionUrl": "Winners"
          }
        }
      }
    ]
  },
  "winnerCommonConfig": {
    "tournamentType": "nps",
    "tournamentTitle": "NPS Winners 2025",
    "tournamentNameColor": "#D3D3D4",
    "screenHeaderImageUri": "https://example.com/assets/nps-post-winners/nps-logo.webp",
    "screenBackgroundImageUri": "https://example.com/assets/nps-post-winners/nps-winner-bg.webp",
    "screenHeaderTitle": "NPS Winners 2025",
    "screenHeaderTitleColor": "#FFFFFF",
    "paginationDot": {
      "dotsBGColor": "#2F1C01",
      "dotsTitleColor": "#FFFFFF"
    },
    "bottomButton": {
      "buttonTitle": "View All Events",
      "buttonTitleColor": "#D3D3D4",
      "buttonIconUri": "more-arrow",
      "redirectionDetails": {
        "redirectionType": "SCREEN",
        "redirectionParams": {
          "redirectionUrl": "WinnersAllEvents"
        }
      }
    },
    "headerTitleImagesUri": {
      "winnerImageUri": "https://example.com/assets/nps-post-winners/nps-champion-text.webp",
      "medallistImageUri": "https://example.com/assets/nps-post-winners/nps-medallists-text.webp",
      "summaryImageUri": "https://example.com/assets/nps-post-winners/nps-summary-text.webp",
      "statsImageUri": "https://example.com/assets/nps-post-winners/nps-prize-text.webp"
    },
    "headerSubtitleImageUri": "https://example.com/assets/nps-post-winners/nps-title-arrow.webp"
  },
  "winnerStatsConfig": [
    {
      "id": "stats-card",
      "cardBGUri": "https://example.com/assets/nps-post-winners/nps-winner-card-bg.webp",
      "cardHeaderUri": "https://example.com/assets/nps-post-winners/nps-winner-last-avatar.webp",
      "tournamentName": "ALL EVENTS 2025"
    },
    {
      "id": "stats-card",
      "cardBGUri": "https://example.com/assets/nps-post-winners/nps-winner-card-bg.webp",
      "cardHeaderUri": "https://example.com/assets/nps-post-winners/nps-winner-last-avatar.webp",
      "tournamentName": "ALL EVENTS 2025"
    }
  ],
  "winnerConfig": {
    "cardBGImage": "https://example.com/assets/nps-post-winners/nps-winner-card-bg.webp",
    "avatar": {
      "borderColor": "#E9C372",
      "userNameColor": "#E9C372",
      "userIdColor": "#BDBDBE",
      "userAddressImageUri": "location-icon",
      "userAddressColor": "#BDBDBE"
    },
    "dividerImage": "https://example.com/assets/nps-post-winners/nps-winner-seperator.webp",
    "winningAmountBGImageUri": "https://example.com/assets/digital-cabinet/won-bg.webp",
    "winningAmountColor": "#E9C372",
    "share": {
      "iconUri": "line-share",
      "iconBackgroundColor": "#0000007A",
      "screenshotName": "nps-2025",
      "shareMessage": "Only the fearless taste true glory. 🏆\n__NAME__ won a whopping amount of ₹__AMOUNT__ in India's biggest tournament series: National Poker Series 🇮🇳 2025, hosted on PokerBaazi.\n\nJoin the action!\nDownload the app: __LINK__"
    },
    "buttons": [
      {
        "buttonTitle": "View Profile",
        "buttonTitleColor": "#E9C372",
        "buttonColor": "",
        "borderColor": "#E9C372",
        "redirectionDetails": {
          "redirectionType": "SCREEN",
          "redirectionParams": {
            "redirectionUrl": "DrawerMyCareer",
            "extraParams": {}
          }
        }
      },
      {
        "buttonTitle": "Final Table",
        "buttonTitleColor": "#212427",
        "buttonColor": "#E9C372",
        "borderColor": "#E9C372",
        "redirectionDetails": {
          "redirectionType": "SCREEN",
          "redirectionParams": {
            "redirectionUrl": "WinnersFinalTable",
            "extraParams": {}
          }
        }
      }
    ]
  },
  "winnerSummaryConfig": {
    "id": "all-events-card",
    "headerSubtitle": "2024",
    "headerSubtitleColor": "#D3D3D4",
    "cardBGUri": "https://example.com/assets/nps-post-winners/nps-winner-card-bg.webp",
    "cardHeaderUri": "https://example.com/assets/nps-post-winners/nps-winner-last-avatar.webp",
    "tournamentName": "ALL EVENTS 2025",
    "buttons": [
      {
        "buttonTitle": "View All Events",
        "buttonTitleColor": "#041B67",
        "buttonColor": "#E9C372",
        "borderColor": "#E9C372",
        "redirectionDetails": {
          "redirectionType": "SCREEN",
          "redirectionParams": {
            "redirectionUrl": "WinnersAllEvents"
          }
        }
      }
    ]
  },
  "winnerFinalTableConfig": {
    "screenBGImageUri": "https://example.com/assets/nps-post-winners/nps-winner-bg.webp",
    "screenHeaderTitle": "Final Table",
    "screenHeaderTitleColor": "#FFFFFF",
    "topSection": {
      "arrowImage": "https://example.com/assets/nps-post-winners/nps-title-arrow.webp",
      "title": "",
      "subTitle": "",
      "titleColor": "#F7D955",
      "subTitleColor": "#FFFFFF"
    },
    "card": {
      "badges": {
        "first": "https://example.com/assets/nps/gold-medal.webp",
        "second": "https://example.com/assets/nps/silver-medal.webp",
        "third": "https://example.com/assets/nps/bronze-medal.webp"
      },
      "rings": {
        "first": "https://example.com/assets/avatar-rings/gold-ring.webp",
        "second": "https://example.com/assets/avatar-rings/silver-ring.webp",
        "third": "https://example.com/assets/avatar-rings/bronze-ring.webp"
      },
      "cardBGUri": {
        "first": "https://example.com/assets/avatar-rings/gold-ring.webp",
        "second": "https://example.com/assets/avatar-rings/silver-ring.webp",
        "third": "https://example.com/assets/avatar-rings/bronze-ring.webp"
      },
      "fullNameColor": "#FFFFFF",
      "userStateColor": "#909293",
      "cardBGColor": "#000",
      "cardBorderColor": "#D3D3D44D",
      "cardTitleColor": "#FFF",
      "amountContainer": {
        "amountColor": "#FFF",
        "amountImageUri": "https://example.com/assets/wpt/cash.webp"
      },
      "dealBGImageUri": "https://example.com/assets/nps/deal.webp",
      "moneyMakerImage": "https://example.com/assets/nps/deal.webp",
      "dealImage": "https://example.com/assets/nps/deal.webp"
    }
  },
  "allEventsConfig": {
    "screenBGImageUri": "https://example.com/assets/nps-post-winners/nps-winner-bg.webp",
    "screenBGColor": "#000000",
    "screenHeaderTitle": "NPS Events 2025",
    "screenHeaderTitleColor": "#FFF",
    "card": {
      "cardBGColor": "#B68E4A14",
      "cardBorderColor": "#B68E4A1F",
      "collapseIcon": {
        "iconUri": "more-arrow"
      },
      "cardTitle": {
        "cardTitleColor": "#F7D955",
        "cardSubTitleColor": "#FFF"
      },
      "cardItem": {
        "cardBGColor": "#000",
        "cardBorderColor": "#D3D3D44D",
        "cardTitleColor": "#FFF",
        "cardSubTitleColor": "#FFF",
        "cashImageUri": "https://example.com/assets/wpt/cash.webp",
        "cardTimerColor": "#7A7C7D"
      }
    }
  }
};
