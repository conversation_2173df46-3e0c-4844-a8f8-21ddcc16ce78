
import React from 'react';
import { useConfig } from '@/contexts/ConfigContext';
import { ConfigProvider } from '@/contexts/ConfigContext';
import Header from '@/components/Layout/Header';
import Dashboard from '@/components/Dashboard/Dashboard';
import ConfigEditor from '@/components/ConfigEditor/ConfigEditor';

const AppContent = () => {
  const { selectedConfig } = useConfig();

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="flex-1">
        {selectedConfig ? <ConfigEditor /> : <Dashboard />}
      </main>
    </div>
  );
};

const Index = () => {
  return (
    <ConfigProvider>
      <AppContent />
    </ConfigProvider>
  );
};

export default Index;
