
import { PublicClientApplication, Configuration, RedirectRequest, PopupRequest } from '@azure/msal-browser';

export interface User {
  id: string;
  email: string;
  name: string;
  provider: 'microsoft' | 'email';
}

export interface AuthConfig {
  clientId: string;
  authority?: string;
  redirectUri?: string;
}

export class AuthService {
  private msalInstance: PublicClientApplication | null = null;
  private config: AuthConfig | null = null;

  async initialize(config: AuthConfig) {
    this.config = config;
    
    const msalConfig: Configuration = {
      auth: {
        clientId: config.clientId,
        authority: config.authority || 'https://login.microsoftonline.com/common',
        redirectUri: config.redirectUri || window.location.origin,
      },
      cache: {
        cacheLocation: 'localStorage',
        storeAuthStateInCookie: false,
      }
    };

    this.msalInstance = new PublicClientApplication(msalConfig);
    await this.msalInstance.initialize();
  }

  async loginWithMicrosoft(): Promise<User | null> {
    if (!this.msalInstance) {
      throw new Error('Auth service not initialized');
    }

    try {
      const loginRequest: PopupRequest = {
        scopes: ['user.read'],
      };

      const response = await this.msalInstance.loginPopup(loginRequest);
      
      if (response.account) {
        const user: User = {
          id: response.account.localAccountId,
          email: response.account.username,
          name: response.account.name || response.account.username,
          provider: 'microsoft'
        };
        
        localStorage.setItem('auth_user', JSON.stringify(user));
        return user;
      }
      
      return null;
    } catch (error) {
      console.error('Microsoft login failed:', error);
      throw error;
    }
  }

  async loginWithEmail(email: string, password: string): Promise<User | null> {
    // Mock email/password authentication - replace with your backend API
    try {
      // This is a mock implementation - replace with actual API call
      if (email && password) {
        const user: User = {
          id: `email_${Date.now()}`,
          email,
          name: email.split('@')[0],
          provider: 'email'
        };
        
        localStorage.setItem('auth_user', JSON.stringify(user));
        return user;
      }
      return null;
    } catch (error) {
      console.error('Email login failed:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    if (this.msalInstance) {
      const accounts = this.msalInstance.getAllAccounts();
      if (accounts.length > 0) {
        await this.msalInstance.logoutPopup({
          account: accounts[0]
        });
      }
    }
    
    localStorage.removeItem('auth_user');
  }

  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('auth_user');
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch {
        return null;
      }
    }
    return null;
  }

  isAuthenticated(): boolean {
    return this.getCurrentUser() !== null;
  }
}

export const authService = new AuthService();
