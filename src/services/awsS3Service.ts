
import { 
  S3Client, 
  PutObjectCommand, 
  GetO<PERSON>Command, 
  DeleteObjectCommand, 
  ListObjectsV2Command,
  HeadObjectCommand,
  PutObjectCommandInput,
  GetObjectCommandInput,
  DeleteObjectCommandInput,
  ListObjectsV2CommandInput,
  HeadObjectCommandInput
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

export interface S3Config {
  region: string;
  accessKeyId?: string; // Optional for IAM role credentials
  secretAccessKey?: string; // Optional for IAM role credentials
  bucketName: string;
  endpoint?: string; // Optional for custom S3-compatible services
}

export interface S3File {
  key: string;
  size?: number;
  lastModified?: Date;
  etag?: string;
}

export interface S3UploadOptions {
  contentType?: string;
  metadata?: Record<string, string>;
  acl?: 'private' | 'public-read' | 'public-read-write';
}

export class AWSS3Service {
  private s3Client: S3Client;
  private bucketName: string;

  constructor(config: S3Config) {
    this.bucketName = config.bucketName;
    
    this.s3Client = new S3Client({
      region: config.region,
      // If accessKeyId is not provided, AWS SDK will automatically use IAM role credentials
      ...(config.accessKeyId && config.secretAccessKey ? {
        credentials: {
          accessKeyId: config.accessKeyId,
          secretAccessKey: config.secretAccessKey,
        }
      } : {}),
      ...(config.endpoint && { endpoint: config.endpoint }),
    });
  }

  /**
   * Upload a file to S3
   */
  async uploadFile(
    key: string, 
    content: string | Uint8Array | Buffer, 
    options: S3UploadOptions = {}
  ): Promise<{ success: boolean; key: string; url?: string }> {
    try {
      const uploadParams: PutObjectCommandInput = {
        Bucket: this.bucketName,
        Key: key,
        Body: content,
        ContentType: options.contentType || 'application/octet-stream',
        Metadata: options.metadata,
        ...(options.acl && { ACL: options.acl }),
      };

      const command = new PutObjectCommand(uploadParams);
      await this.s3Client.send(command);

      console.log(`✅ Successfully uploaded file to S3: ${key}`);
      
      return {
        success: true,
        key,
        url: `https://${this.bucketName}.s3.amazonaws.com/${key}`
      };
    } catch (error) {
      console.error('❌ S3 upload failed:', error);
      return {
        success: false,
        key
      };
    }
  }

  /**
   * Upload JSON data to S3
   */
  async uploadJSON(
    key: string, 
    data: any, 
    options: Omit<S3UploadOptions, 'contentType'> = {}
  ): Promise<{ success: boolean; key: string; url?: string }> {
    const jsonContent = JSON.stringify(data, null, 2);
    return this.uploadFile(key, jsonContent, {
      ...options,
      contentType: 'application/json'
    });
  }

  /**
   * Download a file from S3
   */
  async downloadFile(key: string): Promise<{ success: boolean; content?: string; error?: string }> {
    try {
      const downloadParams: GetObjectCommandInput = {
        Bucket: this.bucketName,
        Key: key,
      };

      const command = new GetObjectCommand(downloadParams);
      const response = await this.s3Client.send(command);

      if (response.Body) {
        const content = await response.Body.transformToString();
        console.log(`✅ Successfully downloaded file from S3: ${key}`);
        return {
          success: true,
          content
        };
      }

      return {
        success: false,
        error: 'No content in response'
      };
    } catch (error) {
      console.error('❌ S3 download failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Download and parse JSON from S3
   */
  async downloadJSON(key: string): Promise<{ success: boolean; data?: any; error?: string }> {
    const result = await this.downloadFile(key);
    
    if (!result.success || !result.content) {
      return {
        success: false,
        error: result.error || 'Failed to download file'
      };
    }

    try {
      const data = JSON.parse(result.content);
      return {
        success: true,
        data
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to parse JSON content'
      };
    }
  }

  /**
   * Delete a file from S3
   */
  async deleteFile(key: string): Promise<{ success: boolean; error?: string }> {
    try {
      const deleteParams: DeleteObjectCommandInput = {
        Bucket: this.bucketName,
        Key: key,
      };

      const command = new DeleteObjectCommand(deleteParams);
      await this.s3Client.send(command);

      console.log(`✅ Successfully deleted file from S3: ${key}`);
      return { success: true };
    } catch (error) {
      console.error('❌ S3 delete failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * List files in S3 with optional prefix
   */
  async listFiles(prefix?: string, maxKeys: number = 1000): Promise<{ success: boolean; files?: S3File[]; error?: string }> {
    try {
      const listParams: ListObjectsV2CommandInput = {
        Bucket: this.bucketName,
        Prefix: prefix,
        MaxKeys: maxKeys,
      };

      const command = new ListObjectsV2Command(listParams);
      const response = await this.s3Client.send(command);

      const files: S3File[] = (response.Contents || []).map(obj => ({
        key: obj.Key || '',
        size: obj.Size,
        lastModified: obj.LastModified,
        etag: obj.ETag,
      }));

      console.log(`✅ Successfully listed ${files.length} files from S3`);
      return {
        success: true,
        files
      };
    } catch (error) {
      console.error('❌ S3 list failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Check if a file exists in S3
   */
  async fileExists(key: string): Promise<{ exists: boolean; metadata?: any; error?: string }> {
    try {
      const headParams: HeadObjectCommandInput = {
        Bucket: this.bucketName,
        Key: key,
      };

      const command = new HeadObjectCommand(headParams);
      const response = await this.s3Client.send(command);

      return {
        exists: true,
        metadata: {
          size: response.ContentLength,
          lastModified: response.LastModified,
          contentType: response.ContentType,
          etag: response.ETag,
        }
      };
    } catch (error: any) {
      if (error.name === 'NotFound' || error.$metadata?.httpStatusCode === 404) {
        return { exists: false };
      }
      
      console.error('❌ S3 head object failed:', error);
      return {
        exists: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Generate a presigned URL for temporary access
   */
  async getPresignedUrl(
    key: string, 
    operation: 'get' | 'put' = 'get', 
    expiresIn: number = 3600
  ): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      let command;
      
      if (operation === 'get') {
        command = new GetObjectCommand({
          Bucket: this.bucketName,
          Key: key,
        });
      } else {
        command = new PutObjectCommand({
          Bucket: this.bucketName,
          Key: key,
        });
      }

      const url = await getSignedUrl(this.s3Client, command, { expiresIn });
      
      return {
        success: true,
        url
      };
    } catch (error) {
      console.error('❌ Failed to generate presigned URL:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Generate S3 path for organized file storage
   */
  generateS3Path(environment: string, documentType: string, documentId: string, version?: number): string {
    const basePath = `${environment}/${documentType}/${documentId}`;
    return version ? `${basePath}/v${version}/` : `${basePath}/`;
  }

  /**
   * Generate filename with version
   */
  generateFileName(documentId: string, version: number, extension: string = 'json'): string {
    return `${documentId}_v${version}.${extension}`;
  }

  /**
   * Generate metadata filename
   */
  generateMetadataFileName(documentId: string, version: number): string {
    return `${documentId}_v${version}_metadata.json`;
  }

  /**
   * Copy file within S3
   */
  async copyFile(sourceKey: string, destinationKey: string): Promise<{ success: boolean; error?: string }> {
    try {
      // First download the source file
      const downloadResult = await this.downloadFile(sourceKey);
      if (!downloadResult.success) {
        return {
          success: false,
          error: `Failed to download source file: ${downloadResult.error}`
        };
      }

      // Then upload to destination
      const uploadResult = await this.uploadFile(destinationKey, downloadResult.content!);
      if (!uploadResult.success) {
        return {
          success: false,
          error: 'Failed to upload to destination'
        };
      }

      console.log(`✅ Successfully copied file from ${sourceKey} to ${destinationKey}`);
      return { success: true };
    } catch (error) {
      console.error('❌ S3 copy failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get bucket name
   */
  getBucketName(): string {
    return this.bucketName;
  }
}

// Export a factory function for creating S3 service instances
export const createS3Service = (config: S3Config): AWSS3Service => {
  return new AWSS3Service(config);
};

// Export default configuration helper
export const getDefaultS3Config = (): Partial<S3Config> => ({
  region: 'us-east-1',
  // Note: In production, these should come from environment variables or Supabase secrets
  // accessKeyId and secretAccessKey should be provided by the user
});
