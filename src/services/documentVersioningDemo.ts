
import { S3Service } from './s3Service';
import { DocumentVersioningService } from './documentVersioningService';
import { CreateDocumentRequest, UpdateDocumentRequest } from '@/types/versioning';

export class DocumentVersioningDemo {
  private versioningService: DocumentVersioningService;

  constructor() {
    // Initialize S3 service with demo configuration
    const s3Config = {
      bucketName: 'my-config-documents',
      region: 'us-east-1',
      accessKeyId: 'demo-access-key',
      secretAccessKey: 'demo-secret-key'
    };

    const s3Service = new S3Service(s3Config);
    this.versioningService = new DocumentVersioningService(s3Service);
  }

  async runDemo(): Promise<void> {
    console.log('🚀 Starting Document Versioning Demo');
    console.log('=====================================');

    try {
      // Demo 1: Create a new document
      await this.demoCreateDocument();
      
      // Demo 2: Update the document (create new version)
      await this.demoUpdateDocument();
      
      // Demo 3: Get version history
      await this.demoGetVersionHistory();
      
      // Demo 4: Get specific version
      await this.demoGetSpecificVersion();

    } catch (error) {
      console.error('Demo failed:', error);
    }
  }

  private async demoCreateDocument(): Promise<void> {
    console.log('\n📝 Demo 1: Creating new document');
    console.log('--------------------------------');

    const createRequest: CreateDocumentRequest = {
      documentId: 'winner-popup-config',
      documentType: 'modal-config',
      environment: 'dev',
      author: '<EMAIL>',
      changeDescription: 'Initial configuration for winner popup modal',
      data: {
        winnerPopupModalConfig: {
          title: 'Congratulations!',
          subtitle: 'You won the game',
          backgroundColor: '#4F46E5',
          showAnimation: true,
          buttons: [
            { 
              label: 'Play Again', 
              action: 'restart', 
              color: '#10B981' 
            },
            { 
              label: 'Share', 
              action: 'share', 
              color: '#6B7280' 
            }
          ]
        }
      }
    };

    const result = await this.versioningService.createDocument(createRequest);
    
    if (result.success) {
      console.log('✅ Document created successfully!');
      console.log('Document ID:', result.documentVersion?.id);
      console.log('Version:', result.documentVersion?.version);
      console.log('Author:', result.documentVersion?.author);
      console.log('Timestamp:', result.documentVersion?.timestamp);
    } else {
      console.log('❌ Failed to create document:', result.error);
    }
  }

  private async demoUpdateDocument(): Promise<void> {
    console.log('\n🔄 Demo 2: Updating document (new version)');
    console.log('-------------------------------------------');

    const updateRequest: UpdateDocumentRequest = {
      documentId: 'winner-popup-config',
      documentType: 'modal-config',
      environment: 'dev',
      author: '<EMAIL>',
      changeDescription: 'Added new button and updated colors',
      data: {
        winnerPopupModalConfig: {
          title: 'Amazing Victory!',
          subtitle: 'You are the champion',
          backgroundColor: '#7C3AED',
          showAnimation: true,
          showConfetti: true, // New field
          buttons: [
            { 
              label: 'Play Again', 
              action: 'restart', 
              color: '#059669' 
            },
            { 
              label: 'Share Victory', 
              action: 'share', 
              color: '#DC2626' 
            },
            { 
              label: 'View Stats', // New button
              action: 'stats', 
              color: '#2563EB' 
            }
          ]
        }
      }
    };

    const result = await this.versioningService.updateDocument(updateRequest);
    
    if (result.success) {
      console.log('✅ Document updated successfully!');
      console.log('Document ID:', result.documentVersion?.id);
      console.log('New Version:', result.documentVersion?.version);
      console.log('Author:', result.documentVersion?.author);
      console.log('Change Description:', result.documentVersion?.changeDescription);
      console.log('Timestamp:', result.documentVersion?.timestamp);
    } else {
      console.log('❌ Failed to update document:', result.error);
    }
  }

  private async demoGetVersionHistory(): Promise<void> {
    console.log('\n📚 Demo 3: Getting version history');
    console.log('----------------------------------');

    const history = await this.versioningService.getVersionHistory(
      'winner-popup-config',
      'modal-config',
      'dev'
    );

    if (history) {
      console.log('✅ Version history retrieved!');
      console.log('Document ID:', history.documentId);
      console.log('Document Type:', history.documentType);
      console.log('Environment:', history.environment);
      console.log('Latest Version:', history.latestVersion);
      console.log('Total Versions:', history.versions.length);
      
      console.log('\nVersion Details:');
      history.versions.forEach(version => {
        console.log(`  v${version.version}: ${version.changeDescription} (${version.author}) - ${version.timestamp}`);
      });
    } else {
      console.log('❌ Failed to get version history');
    }
  }

  private async demoGetSpecificVersion(): Promise<void> {
    console.log('\n🎯 Demo 4: Getting specific version');
    console.log('-----------------------------------');

    // Get version 1
    const version1 = await this.versioningService.getDocumentByVersion(
      'winner-popup-config',
      'modal-config',
      'dev',
      1
    );

    if (version1) {
      console.log('✅ Version 1 retrieved!');
      console.log('Document ID:', version1.id);
      console.log('Version:', version1.version);
      console.log('Author:', version1.author);
      console.log('Change Description:', version1.changeDescription);
      console.log('Data keys:', Object.keys(version1.data));
    } else {
      console.log('❌ Failed to get version 1');
    }

    // Get version 2
    const version2 = await this.versioningService.getDocumentByVersion(
      'winner-popup-config',
      'modal-config',
      'dev',
      2
    );

    if (version2) {
      console.log('\n✅ Version 2 retrieved!');
      console.log('Document ID:', version2.id);
      console.log('Version:', version2.version);
      console.log('Author:', version2.author);
      console.log('Change Description:', version2.changeDescription);
      console.log('Data keys:', Object.keys(version2.data));
      
      // Show differences
      const v2Config = version2.data.winnerPopupModalConfig;
      console.log('New features in v2:');
      console.log('  - Show Confetti:', v2Config.showConfetti);
      console.log('  - Updated Title:', v2Config.title);
      console.log('  - Number of buttons:', v2Config.buttons.length);
    } else {
      console.log('❌ Failed to get version 2');
    }
  }
}

// Export function to run the demo
export const runDocumentVersioningDemo = async (): Promise<void> => {
  const demo = new DocumentVersioningDemo();
  await demo.runDemo();
};
