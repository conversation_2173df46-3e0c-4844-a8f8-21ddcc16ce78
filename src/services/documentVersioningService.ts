
import { 
  DocumentVersion, 
  VersionHistory, 
  CreateDocumentRequest, 
  UpdateDocumentRequest, 
  DocumentResponse 
} from '@/types/versioning';
import { S3Service } from './s3Service';
import { DocumentOperations } from './versioning/documentOperations';
import { VersionManager } from './versioning/versionManager';
import { DocumentRetrieval } from './versioning/documentRetrieval';

export class DocumentVersioningService {
  private documentOperations: DocumentOperations;
  private versionManager: VersionManager;
  private documentRetrieval: DocumentRetrieval;

  constructor(s3Service: S3Service) {
    this.documentOperations = new DocumentOperations(s3Service);
    this.versionManager = new VersionManager(s3Service);
    this.documentRetrieval = new DocumentRetrieval(s3Service);
  }

  async createDocument(request: CreateDocumentRequest): Promise<DocumentResponse> {
    return this.documentOperations.createDocument(request);
  }

  async updateDocument(request: UpdateDocumentRequest): Promise<DocumentResponse> {
    return this.documentOperations.updateDocument(request);
  }

  async getVersionHistory(
    documentId: string, 
    documentType: string, 
    environment: 'dev' | 'staging' | 'prod'
  ): Promise<VersionHistory | null> {
    return this.versionManager.getVersionHistory(documentId, documentType, environment);
  }

  async getDocumentByVersion(
    documentId: string,
    documentType: string,
    environment: 'dev' | 'staging' | 'prod',
    version: number
  ): Promise<DocumentVersion | null> {
    return this.documentRetrieval.getDocumentByVersion(documentId, documentType, environment, version);
  }
}
