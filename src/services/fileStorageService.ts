
import { S3Service, S3Config } from './s3Service';

export interface StoredConfiguration {
  id: string;
  name: string;
  environment: 'dev' | 'staging' | 'prod';
  jsonData: Record<string, any>;
  lastModified: string;
  version: number;
}

export class FileStorageService {
  private s3Service: S3Service | null = null;

  constructor(s3Config?: S3Config) {
    if (s3Config) {
      try {
        this.s3Service = new S3Service(s3Config);
        console.log('✅ FileStorageService initialized with S3 backend using IAM role credentials');
      } catch (error) {
        console.error('❌ Failed to initialize S3 with IAM role credentials:', error);
        throw error; // Don't fallback to localStorage, fail fast
      }
    } else {
      throw new Error('S3 config is required - localStorage fallback has been removed');
    }
  }

  async saveConfiguration(config: StoredConfiguration): Promise<boolean> {
    return this.saveToS3(config);
  }

  async loadAllConfigurations(): Promise<StoredConfiguration[]> {
    return this.loadFromS3();
  }

  async loadConfigurationsByEnvironment(environment: 'dev' | 'staging' | 'prod'): Promise<StoredConfiguration[]> {
    const allConfigs = await this.loadAllConfigurations();
    return allConfigs.filter(config => config.environment === environment);
  }

  async deleteConfiguration(id: string): Promise<boolean> {
    return this.deleteFromS3(id);
  }

  async updateConfiguration(id: string, updates: Partial<StoredConfiguration>): Promise<boolean> {
    return this.updateInS3(id, updates);
  }

  // S3 implementation methods
  private async saveToS3(config: StoredConfiguration): Promise<boolean> {
    try {
      const key = this.generateS3Key(config.environment, config.id);
      const success = await this.s3Service!.uploadJSON(key, config);
      
      if (success) {
        console.log(`✅ Configuration saved to S3: ${config.name}`);
        return true;
      }
      
      console.error('❌ Failed to save configuration to S3');
      return false;
    } catch (error) {
      console.error('❌ S3 save error:', error);
      return false;
    }
  }

  private async loadFromS3(): Promise<StoredConfiguration[]> {
    try {
      const allConfigs: StoredConfiguration[] = [];
      const environments: ('dev' | 'staging' | 'prod')[] = ['dev', 'staging', 'prod'];
      
      for (const env of environments) {
        const prefix = this.generateS3Prefix(env);
        const files = await this.s3Service!.listFiles(prefix);
        
        for (const fileKey of files) {
          const config = await this.s3Service!.downloadJSON(fileKey);
          if (config) {
            allConfigs.push(config as StoredConfiguration);
          }
        }
      }
      
      console.log(`✅ Loaded ${allConfigs.length} configurations from S3`);
      return allConfigs;
    } catch (error) {
      console.error('❌ S3 load error:', error);
      return [];
    }
  }

  private async deleteFromS3(id: string): Promise<boolean> {
    try {
      // Find the configuration first to get its environment
      const allConfigs = await this.loadFromS3();
      const config = allConfigs.find(c => c.id === id);
      
      if (!config) {
        console.warn(`⚠️ Configuration ${id} not found for deletion`);
        return false;
      }
      
      const key = this.generateS3Key(config.environment, id);
      const success = await this.s3Service!.deleteFile(key);
      
      if (success) {
        console.log(`✅ Configuration deleted from S3: ${id}`);
        return true;
      }
      
      console.error('❌ Failed to delete configuration from S3');
      return false;
    } catch (error) {
      console.error('❌ S3 delete error:', error);
      return false;
    }
  }

  private async updateInS3(id: string, updates: Partial<StoredConfiguration>): Promise<boolean> {
    try {
      // Load existing configuration
      const allConfigs = await this.loadFromS3();
      const existingConfig = allConfigs.find(c => c.id === id);
      
      if (!existingConfig) {
        console.warn(`⚠️ Configuration ${id} not found for update`);
        return false;
      }
      
      // Apply updates
      const updatedConfig = {
        ...existingConfig,
        ...updates,
        lastModified: new Date().toISOString()
      };
      
      // Save updated configuration
      return this.saveToS3(updatedConfig);
    } catch (error) {
      console.error('❌ S3 update error:', error);
      return false;
    }
  }


  // S3 path generation helpers
  private generateS3Key(environment: string, configId: string): string {
    return `configurations/${environment}/${configId}.json`;
  }

  private generateS3Prefix(environment: string): string {
    return `configurations/${environment}/`;
  }

  // Method to check if S3 is available
  isS3Available(): boolean {
    return this.s3Service !== null;
  }
}
