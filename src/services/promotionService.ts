import { FileStorageService, StoredConfiguration } from './fileStorageService';
import { S3Config, S3Service } from './s3Service';

export interface PromotionCandidate {
  id: string;
  name: string;
  sourceEnvironment: 'dev' | 'staging' | 'prod';
  targetEnvironment: 'staging' | 'prod';
  sourceConfig: StoredConfiguration;
  targetConfig?: StoredConfiguration;
  hasChanges: boolean;
  promotedConfigUrl?: string;
}

export interface PromotionDiff {
  added: Record<string, any>;
  modified: Record<string, any>;
  removed: Record<string, any>;
}

export class PromotionService {
  private fileStorageService: FileStorageService;
  private s3Service: S3Service | null = null;

  constructor(s3Config?: S3Config) {
    this.fileStorageService = new FileStorageService(s3Config);
    if (s3Config) {
      this.s3Service = new S3Service(s3Config);
    }
  }

  async getPromotionCandidates(environment: 'dev' | 'staging'): Promise<PromotionCandidate[]> {
    const targetEnv = environment === 'dev' ? 'staging' : 'prod';
    const sourceConfigs = await this.fileStorageService.loadConfigurationsByEnvironment(environment);
    const targetConfigs = await this.fileStorageService.loadConfigurationsByEnvironment(targetEnv);
    
    const candidates: PromotionCandidate[] = [];

    for (const sourceConfig of sourceConfigs) {
      const targetConfig = targetConfigs.find(config => config.name === sourceConfig.name);
      
      // Generate promoted config URL if S3 is available
      const promotedConfigUrl = this.s3Service && (targetEnv === 'staging' || targetEnv === 'prod') 
        ? this.s3Service.generatePromotedConfigUrl(targetEnv, sourceConfig.name)
        : undefined;
      
      candidates.push({
        id: sourceConfig.id,
        name: sourceConfig.name,
        sourceEnvironment: environment,
        targetEnvironment: targetEnv,
        sourceConfig,
        targetConfig,
        hasChanges: !targetConfig || this.hasConfigurationChanges(sourceConfig, targetConfig),
        promotedConfigUrl
      });
    }

    return candidates;
  }

  generateDiff(sourceConfig: StoredConfiguration, targetConfig?: StoredConfiguration): PromotionDiff {
    const sourceData = sourceConfig.jsonData;
    const targetData = targetConfig?.jsonData || {};

    const diff: PromotionDiff = {
      added: {},
      modified: {},
      removed: {}
    };

    // Find added and modified properties
    this.compareObjects(sourceData, targetData, '', diff.added, diff.modified);
    
    // Find removed properties
    this.compareObjects(targetData, sourceData, '', diff.removed, {});

    return diff;
  }

  async promoteConfiguration(candidate: PromotionCandidate): Promise<boolean> {
    try {
      const promotedConfig: StoredConfiguration = {
        id: candidate.targetConfig?.id || `config-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: candidate.name,
        environment: candidate.targetEnvironment,
        jsonData: candidate.sourceConfig.jsonData,
        lastModified: new Date().toISOString(),
        version: candidate.targetConfig ? candidate.targetConfig.version + 1 : 1
      };

      // Save to the regular location
      const success = await this.fileStorageService.saveConfiguration(promotedConfig);
      
      if (success && this.s3Service && (candidate.targetEnvironment === 'staging' || candidate.targetEnvironment === 'prod')) {
        // Also save to the promoted config location with consistent URL
        const promotedKey = this.s3Service.generatePromotedConfigKey(
          candidate.targetEnvironment, 
          candidate.name
        );
        await this.s3Service.uploadJSON(promotedKey, promotedConfig.jsonData);
      }
      
      if (success) {
        console.log(`✅ Configuration "${candidate.name}" promoted from ${candidate.sourceEnvironment} to ${candidate.targetEnvironment}`);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('❌ Error promoting configuration:', error);
      return false;
    }
  }

  private hasConfigurationChanges(source: StoredConfiguration, target: StoredConfiguration): boolean {
    return JSON.stringify(source.jsonData) !== JSON.stringify(target.jsonData);
  }

  private compareObjects(
    source: any, 
    target: any, 
    path: string, 
    added: Record<string, any>, 
    modified: Record<string, any>
  ): void {
    for (const key in source) {
      const currentPath = path ? `${path}.${key}` : key;
      const sourceValue = source[key];
      const targetValue = target[key];

      if (!(key in target)) {
        added[currentPath] = sourceValue;
      } else if (typeof sourceValue === 'object' && sourceValue !== null && typeof targetValue === 'object' && targetValue !== null) {
        if (Array.isArray(sourceValue) && Array.isArray(targetValue)) {
          if (JSON.stringify(sourceValue) !== JSON.stringify(targetValue)) {
            modified[currentPath] = { from: targetValue, to: sourceValue };
          }
        } else {
          this.compareObjects(sourceValue, targetValue, currentPath, added, modified);
        }
      } else if (sourceValue !== targetValue) {
        modified[currentPath] = { from: targetValue, to: sourceValue };
      }
    }
  }
}
