import { AWSS3Service, S3Config, createS3Service } from './awsS3Service';

// Re-export types for backward compatibility
export type { S3Config } from './awsS3Service';

// S3Service that uses the actual AWS S3 implementation
export class S3Service {
  private awsS3Service: AWSS3Service;

  constructor(config: S3Config) {
    this.awsS3Service = createS3Service(config);
  }

  async uploadFile(key: string, content: string): Promise<boolean> {
    const result = await this.awsS3Service.uploadFile(key, content, {
      contentType: 'application/json'
    });
    return result.success;
  }

  async downloadFile(key: string): Promise<string | null> {
    const result = await this.awsS3Service.downloadFile(key);
    return result.success ? result.content || null : null;
  }

  async listFiles(prefix: string): Promise<string[]> {
    const result = await this.awsS3Service.listFiles(prefix);
    return result.success ? result.files?.map(file => file.key) || [] : [];
  }

  async fileExists(key: string): Promise<boolean> {
    const result = await this.awsS3Service.fileExists(key);
    return result.exists;
  }

  generateS3Path(environment: string, documentType: string, documentId: string, version: number): string {
    return this.awsS3Service.generateS3Path(environment, documentType, documentId, version);
  }

  generateFileName(documentId: string, version: number): string {
    return this.awsS3Service.generateFileName(documentId, version);
  }

  generateMetadataFileName(documentId: string, version: number): string {
    return this.awsS3Service.generateMetadataFileName(documentId, version);
  }

  // Generate consistent promoted config URL that remains the same across versions
  generatePromotedConfigUrl(environment: 'staging' | 'prod', configName: string): string {
    const bucketName = this.awsS3Service.getBucketName();
    const key = `promoted-configs/${environment}/${configName}.json`;
    return `https://${bucketName}.s3.amazonaws.com/${key}`;
  }

  // Generate key for promoted config storage
  generatePromotedConfigKey(environment: 'staging' | 'prod', configName: string): string {
    return `promoted-configs/${environment}/${configName}.json`;
  }

  // Additional methods that leverage the AWS S3 service
  async uploadJSON(key: string, data: any): Promise<boolean> {
    const result = await this.awsS3Service.uploadJSON(key, data);
    return result.success;
  }

  async downloadJSON(key: string): Promise<any | null> {
    const result = await this.awsS3Service.downloadJSON(key);
    return result.success ? result.data : null;
  }

  async deleteFile(key: string): Promise<boolean> {
    const result = await this.awsS3Service.deleteFile(key);
    return result.success;
  }

  async copyFile(sourceKey: string, destinationKey: string): Promise<boolean> {
    const result = await this.awsS3Service.copyFile(sourceKey, destinationKey);
    return result.success;
  }

  async getPresignedUrl(key: string, operation: 'get' | 'put' = 'get', expiresIn: number = 3600): Promise<string | null> {
    const result = await this.awsS3Service.getPresignedUrl(key, operation, expiresIn);
    return result.success ? result.url || null : null;
  }
}
