
import { DocumentVersion, DocumentMetadata, CreateDocumentRequest, UpdateDocumentRequest, DocumentResponse } from '@/types/versioning';
import { S3Service } from '../s3Service';
import { MetadataService } from './metadataService';
import { VersionManager } from './versionManager';

export class DocumentOperations {
  private s3Service: S3Service;
  private versionManager: VersionManager;

  constructor(s3Service: S3Service) {
    this.s3Service = s3Service;
    this.versionManager = new VersionManager(s3Service);
  }

  async createDocument(request: CreateDocumentRequest): Promise<DocumentResponse> {
    try {
      console.log('Creating new document:', request.documentId);

      // Check if document already exists
      const exists = await this.versionManager.documentExists(
        request.documentId,
        request.documentType,
        request.environment
      );

      if (exists) {
        return {
          success: false,
          error: `Document ${request.documentId} already exists. Use updateDocument instead.`
        };
      }

      return await this.saveDocumentVersion(request, 1);

    } catch (error) {
      console.error('Error creating document:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async updateDocument(request: UpdateDocumentRequest): Promise<DocumentResponse> {
    try {
      console.log('Updating document:', request.documentId);

      // Check if document exists
      const exists = await this.versionManager.documentExists(
        request.documentId,
        request.documentType,
        request.environment
      );

      if (!exists) {
        return {
          success: false,
          error: `Document ${request.documentId} does not exist. Use createDocument instead.`
        };
      }

      // Determine new version number
      const newVersion = request.version || await this.versionManager.getNextVersion(
        request.documentId,
        request.documentType,
        request.environment
      );

      return await this.saveDocumentVersion(request, newVersion);

    } catch (error) {
      console.error('Error updating document:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  private async saveDocumentVersion(
    request: CreateDocumentRequest | UpdateDocumentRequest,
    version: number
  ): Promise<DocumentResponse> {
    const changeDescription = request.changeDescription || 
      (version === 1 ? 'Initial version' : `Update to version ${version}`);

    // Create document version and metadata
    const documentVersion = MetadataService.createDocumentVersion(
      request.documentId,
      version,
      request.environment,
      request.author,
      changeDescription,
      request.data
    );

    const metadata = MetadataService.createMetadata(
      request.documentId,
      version,
      request.environment,
      request.author,
      changeDescription,
      request.data
    );

    // Generate S3 paths
    const s3Path = this.s3Service.generateS3Path(
      request.environment,
      request.documentType,
      request.documentId,
      version
    );

    const documentKey = s3Path + this.s3Service.generateFileName(request.documentId, version);
    const metadataKey = s3Path + this.s3Service.generateMetadataFileName(request.documentId, version);

    // Upload to S3
    const documentUploaded = await this.s3Service.uploadFile(
      documentKey,
      JSON.stringify(documentVersion.data, null, 2)
    );

    const metadataUploaded = await this.s3Service.uploadFile(
      metadataKey,
      JSON.stringify(metadata, null, 2)
    );

    if (!documentUploaded || !metadataUploaded) {
      return {
        success: false,
        error: 'Failed to upload document to S3'
      };
    }

    console.log('Document saved successfully:', documentVersion.id);

    return {
      success: true,
      documentVersion,
      metadata
    };
  }
}
