
import { DocumentVersion, DocumentMetadata } from '@/types/versioning';
import { S3Service } from '../s3Service';

export class DocumentRetrieval {
  private s3Service: S3Service;

  constructor(s3Service: S3Service) {
    this.s3Service = s3Service;
  }

  async getDocumentByVersion(
    documentId: string,
    documentType: string,
    environment: 'dev' | 'staging' | 'prod',
    version: number
  ): Promise<DocumentVersion | null> {
    try {
      const s3Path = this.s3Service.generateS3Path(environment, documentType, documentId, version);
      const documentKey = s3Path + this.s3Service.generateFileName(documentId, version);
      const metadataKey = s3Path + this.s3Service.generateMetadataFileName(documentId, version);

      const [documentContent, metadataContent] = await Promise.all([
        this.s3Service.downloadFile(documentKey),
        this.s3Service.downloadFile(metadataKey)
      ]);

      if (!documentContent || !metadataContent) {
        return null;
      }

      const data = JSON.parse(documentContent);
      const metadata = JSON.parse(metadataContent) as DocumentMetadata;

      return {
        id: `${documentId}_v${version}`,
        documentId,
        version,
        environment,
        timestamp: metadata.timestamp,
        author: metadata.author,
        changeDescription: metadata.changeDescription,
        data
      };

    } catch (error) {
      console.error('Error getting document by version:', error);
      return null;
    }
  }
}
