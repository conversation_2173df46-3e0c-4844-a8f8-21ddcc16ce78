
import { DocumentMetadata, DocumentVersion } from '@/types/versioning';
import { VersioningUtils } from './utils';

export class MetadataService {
  static createMetadata(
    documentId: string,
    version: number,
    environment: 'dev' | 'staging' | 'prod',
    author: string,
    changeDescription: string,
    data: Record<string, any>
  ): DocumentMetadata {
    const jsonString = JSON.stringify(data);
    
    return {
      documentId,
      version,
      environment,
      timestamp: VersioningUtils.createTimestamp(),
      author,
      changeDescription,
      fileSize: jsonString.length,
      checksum: VersioningUtils.generateChecksum(jsonString)
    };
  }

  static createDocumentVersion(
    documentId: string,
    version: number,
    environment: 'dev' | 'staging' | 'prod',
    author: string,
    changeDescription: string,
    data: Record<string, any>
  ): DocumentVersion {
    return {
      id: VersioningUtils.generateDocumentId(documentId, version),
      documentId,
      version,
      environment,
      timestamp: VersioningUtils.createTimestamp(),
      author,
      changeDescription,
      data
    };
  }
}
