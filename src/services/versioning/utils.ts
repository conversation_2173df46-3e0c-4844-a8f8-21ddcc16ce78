
export class VersioningUtils {
  static generateChecksum(content: string): string {
    // Simple checksum implementation
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }

  static generateDocumentId(documentId: string, version: number): string {
    return `${documentId}_v${version}`;
  }

  static createTimestamp(): string {
    return new Date().toISOString();
  }
}
