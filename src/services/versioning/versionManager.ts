
import { DocumentMetadata, VersionHistory } from '@/types/versioning';
import { S3Service } from '../s3Service';

export class VersionManager {
  private s3Service: S3Service;

  constructor(s3Service: S3Service) {
    this.s3Service = s3Service;
  }

  async getVersionHistory(
    documentId: string,
    documentType: string,
    environment: 'dev' | 'staging' | 'prod'
  ): Promise<VersionHistory | null> {
    try {
      const basePath = `${environment}/${documentType}/${documentId}/`;
      const files = await this.s3Service.listFiles(basePath);
      
      const metadataFiles = files.filter(file => file.includes('_metadata.json'));
      const versions: DocumentMetadata[] = [];

      for (const file of metadataFiles) {
        const content = await this.s3Service.downloadFile(file);
        if (content) {
          const metadata = JSON.parse(content) as DocumentMetadata;
          versions.push(metadata);
        }
      }

      // Sort by version number
      versions.sort((a, b) => a.version - b.version);

      return {
        documentId,
        documentType,
        environment,
        versions,
        latestVersion: versions.length > 0 ? Math.max(...versions.map(v => v.version)) : 0
      };

    } catch (error) {
      console.error('Error getting version history:', error);
      return null;
    }
  }

  async getNextVersion(
    documentId: string,
    documentType: string,
    environment: 'dev' | 'staging' | 'prod'
  ): Promise<number> {
    const history = await this.getVersionHistory(documentId, documentType, environment);
    return history ? history.latestVersion + 1 : 1;
  }

  async documentExists(
    documentId: string,
    documentType: string,
    environment: 'dev' | 'staging' | 'prod'
  ): Promise<boolean> {
    const history = await this.getVersionHistory(documentId, documentType, environment);
    return history !== null && history.versions.length > 0;
  }
}
