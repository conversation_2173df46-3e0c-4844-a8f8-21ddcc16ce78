
export interface DocumentVersion {
  id: string;
  documentId: string;
  version: number;
  environment: 'dev' | 'staging' | 'prod';
  timestamp: string;
  author: string;
  changeDescription?: string;
  data: Record<string, any>;
}

export interface DocumentMetadata {
  documentId: string;
  version: number;
  environment: 'dev' | 'staging' | 'prod';
  timestamp: string;
  author: string;
  changeDescription?: string;
  fileSize: number;
  checksum: string;
}

export interface VersionHistory {
  documentId: string;
  documentType: string;
  environment: 'dev' | 'staging' | 'prod';
  versions: DocumentMetadata[];
  latestVersion: number;
}

export interface S3Config {
  bucketName: string;
  region: string;
  accessKeyId: string;
  secretAccessKey: string;
}

export interface CreateDocumentRequest {
  documentId: string;
  documentType: string;
  environment: 'dev' | 'staging' | 'prod';
  data: Record<string, any>;
  author: string;
  changeDescription?: string;
}

export interface UpdateDocumentRequest extends CreateDocumentRequest {
  version?: number; // If not provided, will auto-increment
}

export interface DocumentResponse {
  success: boolean;
  documentVersion?: DocumentVersion;
  metadata?: DocumentMetadata;
  error?: string;
}
