
export interface FieldMetadata {
  path: string;
  label: string;
  component: string;
  type: string;
  example?: any;
}

export const generateFieldMetadata = (jsonData: Record<string, any>): FieldMetadata[] => {
  const fields: FieldMetadata[] = [];

  const traverse = (obj: any, currentPath: string = '') => {
    if (obj === null || obj === undefined) return;

    if (Array.isArray(obj)) {
      obj.forEach((item, index) => {
        const arrayPath = `${currentPath}[${index}]`;
        if (typeof item === 'object' && item !== null) {
          traverse(item, arrayPath);
        } else {
          fields.push({
            path: arrayPath,
            label: `Item ${index + 1}`,
            component: inferComponent(arrayPath, item),
            type: typeof item,
            example: item
          });
        }
      });
    } else if (typeof obj === 'object') {
      Object.keys(obj).forEach(key => {
        const newPath = currentPath ? `${currentPath}.${key}` : key;
        const value = obj[key];

        if (Array.isArray(value)) {
          // Handle arrays
          value.forEach((item, index) => {
            const arrayPath = `${newPath}[${index}]`;
            if (typeof item === 'object' && item !== null) {
              traverse(item, arrayPath);
            } else {
              fields.push({
                path: arrayPath,
                label: `${formatLabel(key)} ${index + 1}`,
                component: inferComponent(arrayPath, item),
                type: typeof item,
                example: item
              });
            }
          });
        } else if (typeof value === 'object' && value !== null) {
          // Recursively traverse nested objects
          traverse(value, newPath);
        } else {
          // Handle primitive values
          fields.push({
            path: newPath,
            label: formatLabel(key),
            component: inferComponent(newPath, value),
            type: typeof value,
            example: value
          });
        }
      });
    }
  };

  traverse(jsonData);
  return fields;
};

const formatLabel = (key: string): string => {
  return key
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .replace(/Uri$/, ' URL')
    .replace(/Bg/, 'Background')
    .replace(/Color$/, ' Color');
};

const inferComponent = (path: string, value: any): string => {
  const lowerPath = path.toLowerCase();
  
  if (typeof value === 'boolean') return 'toggle';
  if (typeof value === 'number') return 'numberInput';
  
  if (typeof value === 'string') {
    if (lowerPath.includes('color') || (value && value.startsWith('#'))) return 'colorPicker';
    if (lowerPath.includes('uri') || lowerPath.includes('url') || lowerPath.includes('image')) return 'imageUploader';
    if (lowerPath.includes('redirect') && lowerPath.includes('url')) return 'textInput';
    if (value && value.startsWith('http')) return 'textInput';
    return 'textInput';
  }
  
  return 'textInput';
};
